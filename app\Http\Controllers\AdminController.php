<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use App\Models\Departement;
use App\Models\Reception;
use App\Models\Vehicule;
use App\Models\Employee;
use Illuminate\Support\Facades\Hash;

class AdminController extends Controller
{
    public function AdminDashboard(){
        // Statistiques des départements
        $totalDepartements = Departement::where('statut', 'active')->count();

        // Statistiques des réceptions
        $totalReceptions = Reception::count();
        $receptionsTerminees = Reception::whereNotNull('pv_reception')->count();
        $receptionsEnAttente = $totalReceptions - $receptionsTerminees;

        // Statistiques du personnel DAF (département ID 20)
        $totalEmployeesDAF = Employee::where('dep_id', 20)->count();
        $chauffeurs = Employee::where('dep_id', 20)
            ->where('position_held', 'LIKE', '%chauffeur%')
            ->count();
        $utilisateurs = $totalEmployeesDAF - $chauffeurs;

        // Statistiques des véhicules
        $totalVehicules = Vehicule::count();
        $vehiculesOperationnels = Vehicule::whereIn('etat', ['Bon', 'Passable'])->count();
        $vehiculesEnMaintenance = Vehicule::whereIn('etat', ['En Panne', 'Maintenance', 'Mauvais'])->count();

        // Calcul des pourcentages de tendance (simulation basée sur les données actuelles)
        $tendanceVehicules = $totalVehicules > 0 ? round(($vehiculesOperationnels / $totalVehicules) * 100 - 88, 1) : 0;
        $tendanceReceptions = $totalReceptions > 0 ? round(($receptionsTerminees / $totalReceptions) * 100 - 82, 1) : 0;
        $tendanceDepartements = $totalDepartements > 0 ? round(($totalDepartements / 300) * 100 - 78, 1) : 0;
        $tendancePersonnel = $totalEmployeesDAF > 0 ? round(($chauffeurs / $totalEmployeesDAF) * 100 - 25, 1) : 0;

        // Efficacité du parc (pourcentage de véhicules opérationnels)
        $efficaciteParc = $totalVehicules > 0 ? round(($vehiculesOperationnels / $totalVehicules) * 100) : 0;

        // Données pour le graphique d'évolution des réceptions (par mois)
        $receptionsParMois = [];
        for ($i = 1; $i <= 12; $i++) {
            $count = Reception::whereYear('created_at', date('Y'))
                ->whereMonth('created_at', $i)
                ->count();
            $receptionsParMois[] = $count;
        }

        // Données pour le graphique de répartition des véhicules par état
        $vehiculesParEtat = Vehicule::select('etat', \DB::raw('count(*) as total'))
            ->groupBy('etat')
            ->pluck('total', 'etat')
            ->toArray();

        // S'assurer que tous les états sont présents (même avec 0)
        $etatsStandard = ['Bon', 'Passable', 'En Panne', 'Hors Service'];
        $vehiculesParEtatComplet = [];
        foreach ($etatsStandard as $etat) {
            $vehiculesParEtatComplet[$etat] = $vehiculesParEtat[$etat] ?? 0;
        }

        // Données pour le graphique d'utilisation par département (top 10)
        $vehiculesParDepartement = Vehicule::join('departements', 'vehicules.departement_id', '=', 'departements.id')
            ->select('departements.nom_departement', \DB::raw('count(*) as total'))
            ->groupBy('departements.id', 'departements.nom_departement')
            ->orderBy('total', 'desc')
            ->take(10)
            ->pluck('total', 'nom_departement')
            ->toArray();

        // Données pour le graphique de répartition des employés par département (départements spécifiés)
        $departementsSpecifiques = [
            'CABINET', 'DFV', 'DAEMA', 'DAF', 'DPA', 'DPPSE', 'DPV',
            'DRAEDR-RC', 'DRAEDR-RK', 'DRAEDR-RM', 'DRAEDR-RP', 'DRAEDR-RS',
            'DRH', 'DSP', 'DSID', 'INFA DE TOVE', 'ITRA', 'DEFA', 'ATA'
        ];

        $employesParDepartement = Employee::join('departements', 'employee.dep_id', '=', 'departements.id')
            ->select('departements.nom_departement', \DB::raw('count(*) as total'))
            ->whereIn('departements.nom_departement', $departementsSpecifiques)
            ->groupBy('departements.id', 'departements.nom_departement')
            ->orderBy('total', 'desc')
            ->pluck('total', 'nom_departement')
            ->toArray();

        // Récupérer les 4 réceptions les plus récentes avec leurs relations
        $recentReceptions = Reception::with('departement')
            ->orderBy('created_at', 'desc')
            ->take(4)
            ->get();

        return view('admin.index', compact(
            'totalDepartements',
            'totalReceptions',
            'receptionsTerminees',
            'receptionsEnAttente',
            'totalEmployeesDAF',
            'chauffeurs',
            'utilisateurs',
            'totalVehicules',
            'vehiculesOperationnels',
            'vehiculesEnMaintenance',
            'efficaciteParc',
            'tendanceVehicules',
            'tendanceReceptions',
            'tendanceDepartements',
            'tendancePersonnel',
            'receptionsParMois',
            'vehiculesParEtatComplet',
            'vehiculesParDepartement',
            'employesParDepartement',
            'recentReceptions'
        ));
    }//End Methode

    public function AdminDashboardLogical(){
        // Statistiques des départements
        $totalDepartements = Departement::where('statut', 'active')->count();

        // Statistiques des réceptions
        $totalReceptions = Reception::count();
        $receptionsPendantes = Reception::where('statut', 'en_attente')->count();
        $receptionsCompletes = Reception::where('statut', 'complete')->count();

        // Statistiques des véhicules
        $totalVehicules = Vehicule::count();
        $vehiculesOperationnels = Vehicule::where('etat', 'bon')->count();
        $vehiculesEnMaintenance = Vehicule::where('etat', 'panne')->count();

        // Statistiques des directions
        $directionsActives = $totalDepartements;
        $efficaciteMoyenne = 91; // Valeur simulée

        // Statistiques du personnel
        $totalPersonnel = Employee::count();
        $chauffeurs = Employee::where('poste', 'chauffeur')->count();
        $utilisateurs = Employee::where('poste', '!=', 'chauffeur')->count();

        // Tendances (valeurs simulées pour la démo)
        $tendanceReceptions = 18;
        $tendanceVehicules = 2.9;
        $tendanceDirections = 5.7;
        $tendancePersonnel = -20.3;

        return view('admin.dashboard-logical', compact(
            'totalDepartements', 'totalReceptions', 'totalVehicules', 'totalPersonnel',
            'receptionsPendantes', 'receptionsCompletes', 'vehiculesOperationnels', 'vehiculesEnMaintenance',
            'directionsActives', 'efficaciteMoyenne', 'chauffeurs', 'utilisateurs',
            'tendanceReceptions', 'tendanceVehicules', 'tendanceDirections', 'tendancePersonnel'
        ));
    }//End Methode

    public function AdminLogout(Request $request){
        Auth::guard('web')->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        return redirect('/admin/login');
    }//End Methode

    public function AdminLogin(){
        return view('admin.admin_login');
    }//End Methode

    public function AdminProfile(){
        $id = Auth::user()->id;
        $profileData = User::find($id);
        return view('admin.admin_profile_view', compact('profileData'));
    }//End Methode

    public function AdminProfileStore(Request $request){
        $id = Auth::user()->id;
        $data = User::find($id);
        $data->name = $request->name;
        $data->last_name = $request->last_name;
        $data->email = $request->email;
        $data->phone = $request->phone;
        $data->address = $request->address;

        if ($request->file('photo')) {
            $file = $request->file('photo');
            @unlink(public_path('upload/admin_images/'.$data->photo));
            $filename = date('YmdHi').$file->getClientOriginalName();
            $file->move(public_path('upload/admin_images'),$filename);
            $data['photo'] = $filename;
        }
        $data->save();

        $notification = array(
            'message' => 'Le Profile de l\'Admin a été bien modifié',
            'alert-type' => 'success'
        );

        return redirect()->back()->with($notification);
    }//End Methode

    public function AdminChangePassword(){
        $id = Auth::user()->id;
        $profileData = User::find($id);
        return view('admin.admin_change_password', compact('profileData'));
    }//End Method

    public function AdminPasswordUpdate(Request $request){
        //Validation
        $request->validate([
            'old_password' => 'required',
            'new_password' => 'required|confirmed'
        ]);

        if (!Hash::check($request->old_password, auth::user()->password)){
            $notification = array(
                'message' => 'L\'ancien Mot de Passe ne correspond pas.',
                'alert-type' => 'error'
            );
            return back()->with($notification);
        }

        //Modifier le Nouveau Mot de Passe
        User::whereId(auth()->user()->id)->update([
            'password' => Hash::make($request->new_password)
        ]);
        $notification = array(
            'message' => 'Mot de Passe modifié avec succès.',
            'alert-type' => 'success'
        );
    return back()->with($notification);
    }//EndMethod
}
