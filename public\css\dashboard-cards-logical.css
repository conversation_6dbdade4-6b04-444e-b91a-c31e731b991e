/* Design Logique et Attrayant pour les Cartes Dashboard */
:root {
    /* Système de couleurs logique et cohérent */
    --color-total: #3b82f6;        /* Bleu - Vue d'ensemble */
    --color-total-light: #dbeafe;
    --color-total-dark: #1e40af;
    
    --color-success: #10b981;      /* Vert - Succès/Bon état */
    --color-success-light: #d1fae5;
    --color-success-dark: #047857;
    
    --color-warning: #f59e0b;      /* Orange - Attention/Maintenance */
    --color-warning-light: #fef3c7;
    --color-warning-dark: #d97706;
    
    --color-danger: #ef4444;       /* Rouge - Problème/Critique */
    --color-danger-light: #fecaca;
    --color-danger-dark: #dc2626;
    
    /* Neutrals */
    --color-gray-50: #f9fafb;
    --color-gray-100: #f3f4f6;
    --color-gray-200: #e5e7eb;
    --color-gray-300: #d1d5db;
    --color-gray-400: #9ca3af;
    --color-gray-500: #6b7280;
    --color-gray-600: #4b5563;
    --color-gray-700: #374151;
    --color-gray-800: #1f2937;
    --color-gray-900: #111827;
    
    /* Spacing et sizing */
    --spacing-xs: 0.5rem;
    --spacing-sm: 0.75rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* Border radius */
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 20px;
    
    /* Shadows */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 8px 25px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 12px 40px rgba(0, 0, 0, 0.12);
    
    /* Transitions */
    --transition-fast: 0.15s ease-out;
    --transition-normal: 0.25s ease-out;
    --transition-slow: 0.4s ease-out;
}

/* Container principal */
.dashboard-stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
    padding: var(--spacing-xl) 0;
}

/* Carte principale - Design minimaliste et moderne */
.stat-card {
    background: white;
    border-radius: var(--radius-xl);
    border: 1px solid var(--color-gray-100);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    overflow: hidden;
    position: relative;
    min-height: 160px;
    display: flex;
    flex-direction: column;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--color-gray-200);
}

/* En-tête avec indicateur de couleur */
.stat-card-header {
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
}

/* Indicateur de couleur sur le côté gauche */
.stat-card::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--color-total);
    transition: var(--transition-normal);
}

.stat-card:hover::before {
    width: 6px;
}

/* Icône moderne et épurée */
.stat-card-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    background: var(--color-total);
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
}

.stat-card:hover .stat-card-icon {
    transform: scale(1.05);
    box-shadow: var(--shadow-md);
}

/* Corps de la carte */
.stat-card-body {
    padding: 0 var(--spacing-lg) var(--spacing-lg);
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* Valeur principale - Plus lisible et impactante */
.stat-card-value {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--color-gray-900);
    line-height: 1;
    margin-bottom: var(--spacing-xs);
    letter-spacing: -0.02em;
}

/* Titre descriptif */
.stat-card-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--color-gray-600);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: var(--spacing-md);
}

/* Pied de carte avec tendance */
.stat-card-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: auto;
}

/* Badge de tendance moderne */
.stat-card-trend {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.stat-card-trend.up {
    background: var(--color-success-light);
    color: var(--color-success-dark);
}

.stat-card-trend.down {
    background: var(--color-danger-light);
    color: var(--color-danger-dark);
}

.stat-card-trend.neutral {
    background: var(--color-gray-100);
    color: var(--color-gray-600);
}

/* Texte de contexte */
.stat-card-context {
    font-size: 0.75rem;
    color: var(--color-gray-500);
    font-weight: 500;
}

/* Variantes de couleurs logiques */
.stat-card.vehicles-card::before {
    background: var(--color-total);
}

.stat-card.vehicles-card .stat-card-icon {
    background: var(--color-total);
}

.stat-card.vehicles-card:hover {
    box-shadow: var(--shadow-xl), 0 0 0 1px var(--color-total-light);
}

.stat-card.operational-card::before {
    background: var(--color-success);
}

.stat-card.operational-card .stat-card-icon {
    background: var(--color-success);
}

.stat-card.operational-card:hover {
    box-shadow: var(--shadow-xl), 0 0 0 1px var(--color-success-light);
}

.stat-card.maintenance-card::before {
    background: var(--color-warning);
}

.stat-card.maintenance-card .stat-card-icon {
    background: var(--color-warning);
}

.stat-card.maintenance-card:hover {
    box-shadow: var(--shadow-xl), 0 0 0 1px var(--color-warning-light);
}

.stat-card.critical-card::before {
    background: var(--color-danger);
}

.stat-card.critical-card .stat-card-icon {
    background: var(--color-danger);
}

.stat-card.critical-card:hover {
    box-shadow: var(--shadow-xl), 0 0 0 1px var(--color-danger-light);
}

/* Animations d'entrée */
.stat-card {
    opacity: 0;
    transform: translateY(20px);
    animation: slideInUp 0.6s ease-out forwards;
}

.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }

@keyframes slideInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Animation de comptage */
.stat-card-value[data-count] {
    opacity: 0;
    animation: countUp 1.5s ease-out 0.5s forwards;
}

@keyframes countUp {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Responsive design */
@media (max-width: 768px) {
    .dashboard-stats-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
        padding: var(--spacing-lg) 0;
    }
    
    .stat-card {
        min-height: 140px;
    }
    
    .stat-card-header {
        padding: var(--spacing-md);
    }
    
    .stat-card-body {
        padding: 0 var(--spacing-md) var(--spacing-md);
    }
    
    .stat-card-value {
        font-size: 2rem;
    }
    
    .stat-card-icon {
        width: 40px;
        height: 40px;
        font-size: 1.25rem;
    }
}

@media (max-width: 480px) {
    .stat-card-value {
        font-size: 1.75rem;
    }

    .stat-card-icon {
        width: 36px;
        height: 36px;
        font-size: 1.125rem;
    }
}

/* Styles pour les détails des cartes */
.stat-card-details {
    margin-bottom: var(--spacing-md);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.detail-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.8rem;
    font-weight: var(--font-weight-medium);
    padding: var(--spacing-xs) 0;
    border-radius: var(--radius-sm);
    transition: var(--transition-fast);
}

.detail-item i {
    font-size: 0.9rem;
    opacity: 0.8;
    min-width: 16px;
}

.detail-item.success {
    color: var(--color-success-dark);
    background: var(--color-success-light);
    padding: var(--spacing-xs) var(--spacing-sm);
    margin: 0 -var(--spacing-xs);
}

.detail-item.warning {
    color: var(--color-warning-dark);
    background: var(--color-warning-light);
    padding: var(--spacing-xs) var(--spacing-sm);
    margin: 0 -var(--spacing-xs);
}

.detail-item.neutral {
    color: var(--color-gray-600);
}

/* Amélioration de la structure des cartes */
.stat-card-info {
    flex: 1;
}

.stat-card-header {
    padding: var(--spacing-lg);
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    gap: var(--spacing-md);
}

.stat-card-body {
    padding: 0 var(--spacing-lg) var(--spacing-lg);
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* Effet de clic et interactions */
.stat-card {
    cursor: pointer;
    user-select: none;
}

.stat-card:active {
    transform: translateY(-2px) scale(0.98);
}

.stat-card:focus {
    outline: 2px solid var(--color-total);
    outline-offset: 2px;
}

/* États de chargement */
.stat-card-value.loading {
    opacity: 0.5;
    transform: scale(0.95);
}

/* Indicateurs de statut */
.status-indicator {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--color-success);
    animation: pulse 2s infinite;
}

.status-indicator.warning {
    background: var(--color-warning);
}

.status-indicator.danger {
    background: var(--color-danger);
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.1);
    }
}

/* Améliorations pour l'accessibilité */
@media (prefers-reduced-motion: reduce) {
    .stat-card,
    .stat-card-icon,
    .stat-card-trend,
    .detail-item {
        animation: none;
        transition: none;
    }

    .stat-card:hover {
        transform: none;
    }
}

/* Mode sombre (optionnel) */
@media (prefers-color-scheme: dark) {
    :root {
        --color-gray-50: #1f2937;
        --color-gray-100: #374151;
        --color-gray-200: #4b5563;
        --color-gray-300: #6b7280;
        --color-gray-400: #9ca3af;
        --color-gray-500: #d1d5db;
        --color-gray-600: #e5e7eb;
        --color-gray-700: #f3f4f6;
        --color-gray-800: #f9fafb;
        --color-gray-900: #ffffff;
    }

    .stat-card {
        background: var(--color-gray-50);
        border-color: var(--color-gray-200);
    }
}
