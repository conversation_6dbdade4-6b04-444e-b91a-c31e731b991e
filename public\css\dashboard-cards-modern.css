/* Dashboard Cards Modern Design */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --success-gradient: linear-gradient(135deg, #00f5a0 0%, #00d9f5 100%);
    --warning-gradient: linear-gradient(135deg, #ffa726 0%, #ff7043 100%);
    --danger-gradient: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    --info-gradient: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    --purple-gradient: linear-gradient(135deg, #a855f7 0%, #8b5cf6 100%);
    --teal-gradient: linear-gradient(135deg, #14b8a6 0%, #06b6d4 100%);
    --orange-gradient: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
    
    --card-shadow: 0 10px 40px rgba(0,0,0,0.1);
    --card-shadow-hover: 0 20px 60px rgba(0,0,0,0.15);
    --card-border-radius: 24px;
    --transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Container principal des statistiques */
.dashboard-stats-container {
    padding: 2rem 0;
}

/* Carte statistique moderne */
.stat-card {
    background: #ffffff;
    border-radius: var(--card-border-radius);
    border: 1px solid rgba(0,0,0,0.05);
    box-shadow: var(--card-shadow);
    transition: var(--transition);
    overflow: hidden;
    position: relative;
    height: 300px;
    backdrop-filter: blur(10px);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 8px;
    background: var(--primary-gradient);
    border-radius: 24px 0 0 24px;
    opacity: 1;
    transition: all 0.4s ease;
}

.stat-card:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow: var(--card-shadow-hover);
}

/* Bordures colorées spécialisées au survol */
.stat-card.vehicles-card:hover {
    box-shadow: 0 20px 60px rgba(102, 126, 234, 0.2), 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.stat-card.receptions-card:hover {
    box-shadow: 0 20px 60px rgba(249, 115, 22, 0.2), 0 0 0 2px rgba(249, 115, 22, 0.1);
}

.stat-card.departments-card:hover {
    box-shadow: 0 20px 60px rgba(20, 184, 166, 0.2), 0 0 0 2px rgba(20, 184, 166, 0.1);
}

.stat-card.personnel-card:hover {
    box-shadow: 0 20px 60px rgba(168, 85, 247, 0.2), 0 0 0 2px rgba(168, 85, 247, 0.1);
}

.stat-card:hover::before {
    width: 12px;
    box-shadow: 0 0 20px rgba(0,0,0,0.2);
}

/* En-tête de la carte avec icône */
.stat-card .card-body {
    padding: 2.5rem 2rem 2rem 2.5rem;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
    background: #ffffff;
    border-radius: var(--card-border-radius);
}

/* Coin décoratif en haut à droite */
.stat-card .card-body::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, transparent 50%, rgba(0,0,0,0.03) 50%);
    border-radius: 0 24px 0 60px;
    z-index: 1;
}

/* Coin décoratif en bas à gauche */
.stat-card .card-body::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, rgba(0,0,0,0.02) 50%, transparent 50%);
    border-radius: 40px 0 24px 0;
    z-index: 1;
}

.stat-icon {
    position: absolute;
    top: 2rem;
    right: 2rem;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    color: white;
    box-shadow: 0 8px 32px rgba(0,0,0,0.2);
    backdrop-filter: blur(10px);
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Icônes spécialisées par type */
.bg-vehicles {
    background: var(--primary-gradient);
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4);
}

.bg-receptions {
    background: var(--orange-gradient);
    box-shadow: 0 8px 32px rgba(249, 115, 22, 0.4);
}

.bg-departments {
    background: var(--teal-gradient);
    box-shadow: 0 8px 32px rgba(20, 184, 166, 0.4);
}

.bg-personnel {
    background: var(--purple-gradient);
    box-shadow: 0 8px 32px rgba(168, 85, 247, 0.4);
}

/* Titre de la carte */
.card-title {
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: #475569;
    margin-bottom: 1.5rem;
    margin-top: 0;
    line-height: 1.2;
}

/* Valeur principale */
.stat-value {
    font-size: 3.2rem;
    font-weight: 800;
    color: #1e293b;
    line-height: 1;
    margin-bottom: 1.5rem;
    animation: countUp 2s ease-out;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

@keyframes countUp {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Description des statistiques */
.stat-desc {
    margin-bottom: 2rem;
    line-height: 1.7;
    flex-grow: 1;
}

.stat-desc span {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
}

.stat-desc .text-success {
    font-weight: 600;
    color: #059669 !important;
}

.stat-desc .text-muted {
    color: #64748b !important;
    font-weight: 500;
}

/* Indicateur de tendance */
.trend-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.25rem;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-top: auto;
    border: none;
    position: relative;
    overflow: hidden;
}

.trend-up {
    background: rgba(16, 185, 129, 0.1);
    color: #047857;
    border: 2px solid rgba(16, 185, 129, 0.2);
}

.trend-up::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.05), rgba(5, 150, 105, 0.05));
    z-index: -1;
}

.trend-down {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border: 2px solid rgba(239, 68, 68, 0.2);
}

.trend-down::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.05), rgba(220, 38, 38, 0.05));
    z-index: -1;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Animations d'entrée */
.animate-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 0.8s ease-out forwards;
}

.delay-1 { animation-delay: 0.1s; }
.delay-2 { animation-delay: 0.2s; }
.delay-3 { animation-delay: 0.3s; }
.delay-4 { animation-delay: 0.4s; }

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive design */
@media (max-width: 768px) {
    .stat-card {
        height: 260px;
    }

    .stat-icon {
        width: 65px;
        height: 65px;
        font-size: 2.2rem;
        top: 1.5rem;
        right: 1.5rem;
    }

    .stat-value {
        font-size: 2.8rem;
    }

    .card-body {
        padding: 2rem 1.5rem;
    }

    .card-title {
        font-size: 0.8rem;
        margin-bottom: 1rem;
    }

    .stat-desc span {
        font-size: 0.9rem;
    }

    .trend-indicator {
        font-size: 0.85rem;
        padding: 0.6rem 1rem;
    }
}

/* Effet de brillance au survol */
.stat-card::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: rotate(45deg);
    transition: all 0.6s;
    opacity: 0;
}

.stat-card:hover::after {
    animation: shine 0.6s ease-in-out;
}

@keyframes shine {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); opacity: 0; }
    50% { opacity: 1; }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); opacity: 0; }
}

/* Amélioration des couleurs de texte */
.text-success.fw-bold {
    color: #047857 !important;
    font-weight: 600 !important;
    background: rgba(16, 185, 129, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 8px;
    border-left: 3px solid #10b981;
}

.text-muted {
    color: #64748b !important;
    background: rgba(100, 116, 139, 0.05);
    padding: 0.25rem 0.5rem;
    border-radius: 8px;
    border-left: 3px solid #cbd5e1;
}

/* Styles pour les cartes spécialisées avec bordures colorées */
.stat-card.vehicles-card {
    border-left: 6px solid #667eea;
    border-right: 4px solid rgba(102, 126, 234, 0.3);
    border-top: 2px solid rgba(102, 126, 234, 0.2);
    border-bottom: 2px solid rgba(102, 126, 234, 0.2);
}

.stat-card.vehicles-card::before {
    background: var(--primary-gradient);
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.4);
}

.stat-card.receptions-card {
    border-left: 6px solid #f97316;
    border-right: 4px solid rgba(249, 115, 22, 0.3);
    border-top: 2px solid rgba(249, 115, 22, 0.2);
    border-bottom: 2px solid rgba(249, 115, 22, 0.2);
}

.stat-card.receptions-card::before {
    background: var(--orange-gradient);
    box-shadow: 0 0 20px rgba(249, 115, 22, 0.4);
}

.stat-card.departments-card {
    border-left: 6px solid #14b8a6;
    border-right: 4px solid rgba(20, 184, 166, 0.3);
    border-top: 2px solid rgba(20, 184, 166, 0.2);
    border-bottom: 2px solid rgba(20, 184, 166, 0.2);
}

.stat-card.departments-card::before {
    background: var(--teal-gradient);
    box-shadow: 0 0 20px rgba(20, 184, 166, 0.4);
}

.stat-card.personnel-card {
    border-left: 6px solid #a855f7;
    border-right: 4px solid rgba(168, 85, 247, 0.3);
    border-top: 2px solid rgba(168, 85, 247, 0.2);
    border-bottom: 2px solid rgba(168, 85, 247, 0.2);
}

.stat-card.personnel-card::before {
    background: var(--purple-gradient);
    box-shadow: 0 0 20px rgba(168, 85, 247, 0.4);
}

/* Effet de brillance sur les bordures au survol */
.stat-card:hover {
    border-left-width: 8px;
    border-right-width: 6px;
}

/* Effet de bordure droite au survol */
.stat-card::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 0;
    background: var(--primary-gradient);
    border-radius: 0 24px 24px 0;
    transition: all 0.4s ease;
    opacity: 0;
}

.stat-card:hover::after {
    width: 6px;
    opacity: 1;
}

.stat-card.vehicles-card:hover::after {
    background: var(--primary-gradient);
    box-shadow: 0 0 15px rgba(102, 126, 234, 0.3);
}

.stat-card.receptions-card:hover::after {
    background: var(--orange-gradient);
    box-shadow: 0 0 15px rgba(249, 115, 22, 0.3);
}

.stat-card.departments-card:hover::after {
    background: var(--teal-gradient);
    box-shadow: 0 0 15px rgba(20, 184, 166, 0.3);
}

.stat-card.personnel-card:hover::after {
    background: var(--purple-gradient);
    box-shadow: 0 0 15px rgba(168, 85, 247, 0.3);
}

/* Effets de clic interactifs */
.stat-card:active {
    transform: translateY(-8px) scale(0.98);
    transition: all 0.1s ease;
}

/* Indicateurs de progression circulaires */
.progress-ring {
    position: absolute;
    bottom: 2rem;
    left: 2rem;
    width: 60px;
    height: 60px;
}

.progress-ring-circle {
    stroke: rgba(102, 126, 234, 0.2);
    stroke-width: 4;
    fill: transparent;
    r: 26;
    cx: 30;
    cy: 30;
}

.progress-ring-progress {
    stroke: url(#gradient);
    stroke-width: 4;
    stroke-linecap: round;
    fill: transparent;
    r: 26;
    cx: 30;
    cy: 30;
    stroke-dasharray: 163.36;
    stroke-dashoffset: 163.36;
    transform: rotate(-90deg);
    transform-origin: 50% 50%;
    animation: progressAnimation 2s ease-out forwards;
}

@keyframes progressAnimation {
    to {
        stroke-dashoffset: calc(163.36 - (163.36 * var(--progress)) / 100);
    }
}

/* Badges de notification */
.notification-badge {
    position: absolute;
    top: 1.5rem;
    left: 1.5rem;
    background: #ef4444;
    color: white;
    border-radius: 50%;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: 700;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
    border: 3px solid white;
    z-index: 10;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

/* Effet de vague au clic */
.stat-card {
    position: relative;
    overflow: hidden;
}

.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: rippleAnimation 0.6s linear;
    pointer-events: none;
}

@keyframes rippleAnimation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Amélioration des transitions */
.stat-card * {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Mode sombre adaptatif */
@media (prefers-color-scheme: dark) {
    .stat-card {
        background: linear-gradient(145deg, #1a202c 0%, #2d3748 100%);
        color: white;
    }

    .card-title {
        color: #a0aec0;
    }

    .text-muted {
        color: #718096 !important;
    }
}

/* Animations de chargement */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Micro-interactions */
.stat-value:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    cursor: default;
}

.trend-indicator:hover {
    transform: scale(1.1) !important;
    cursor: pointer;
}

/* Accessibilité améliorée */
.stat-card:focus {
    outline: 3px solid rgba(102, 126, 234, 0.5);
    outline-offset: 2px;
}

@media (prefers-reduced-motion: reduce) {
    .stat-card,
    .stat-icon,
    .trend-indicator,
    .stat-value {
        animation: none !important;
        transition: none !important;
    }
}
