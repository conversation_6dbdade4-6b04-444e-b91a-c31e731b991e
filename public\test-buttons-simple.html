<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Boutons Vue - Simple</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href='https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css' rel='stylesheet'>
    <style>
        .view-switch {
            display: flex;
            align-items: center;
            margin: 2rem 0;
        }

        .view-switch-label {
            font-size: 0.875rem;
            color: #6b7280;
            margin-right: 0.5rem;
        }

        .view-switch-buttons {
            display: flex;
            border-radius: 0.25rem;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }

        .view-switch-btn {
            padding: 0.5rem;
            background-color: white;
            border: 1px solid #d1d5db;
            color: #6b7280;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .view-switch-btn:hover {
            background-color: #f8f9fa;
            border-color: #4f46e5;
            color: #4f46e5;
        }

        .view-switch-btn.active {
            background-color: #4f46e5;
            color: white;
            border-color: #4f46e5;
        }

        .test-area {
            margin: 2rem 0;
            padding: 2rem;
            border: 2px solid #e5e7eb;
            border-radius: 0.5rem;
        }

        #gridView {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 1rem;
        }

        .grid-item {
            background: #f3f4f6;
            padding: 1rem;
            border-radius: 0.5rem;
            text-align: center;
        }

        .d-none {
            display: none !important;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>Test Simple des Boutons de Vue</h1>
        
        <div class="alert alert-info">
            <strong>Instructions :</strong>
            <ul>
                <li>Ouvrez la console (F12)</li>
                <li>Cliquez sur les boutons ci-dessous</li>
                <li>Regardez les logs dans la console</li>
            </ul>
        </div>

        <!-- Switch vue tableau/grille -->
        <div class="view-switch">
            <span class="view-switch-label">Vue :</span>
            <div class="view-switch-buttons">
                <button type="button" class="view-switch-btn" data-view="table" title="Vue tableau">
                    <i class="bx bx-table"></i>
                </button>
                <button type="button" class="view-switch-btn active" data-view="grid" title="Vue grille">
                    <i class="bx bx-grid-alt"></i>
                </button>
            </div>
        </div>

        <!-- Vue grille -->
        <div id="gridView" class="test-area">
            <h3>Vue Grille</h3>
            <div class="grid-item">Élément 1</div>
            <div class="grid-item">Élément 2</div>
            <div class="grid-item">Élément 3</div>
            <div class="grid-item">Élément 4</div>
        </div>

        <!-- Vue tableau -->
        <div id="tableView" class="test-area d-none">
            <h3>Vue Tableau</h3>
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Nom</th>
                        <th>Description</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td>Élément 1</td>
                        <td>Description de l'élément 1</td>
                    </tr>
                    <tr>
                        <td>2</td>
                        <td>Élément 2</td>
                        <td>Description de l'élément 2</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="mt-4">
            <button class="btn btn-primary" onclick="testTableView()">Test Vue Tableau</button>
            <button class="btn btn-secondary" onclick="testGridView()">Test Vue Grille</button>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script>
        console.log('=== DÉBUT DU TEST SIMPLE ===');
        console.log('jQuery disponible:', typeof $ !== 'undefined');

        // Fonction globale de changement de vue
        window.switchView = function(viewType) {
            console.log('=== FONCTION SWITCH VIEW ===');
            console.log('Type de vue:', viewType);

            // Mettre à jour les boutons
            $('.view-switch-btn').removeClass('active');
            $('.view-switch-btn[data-view="' + viewType + '"]').addClass('active');

            if (viewType === 'table') {
                // Afficher la vue tableau
                $('#gridView').addClass('d-none');
                $('#tableView').removeClass('d-none');
                console.log('Vue tableau activée');
            } else {
                // Afficher la vue grille
                $('#tableView').addClass('d-none');
                $('#gridView').removeClass('d-none');
                console.log('Vue grille activée');
            }

            console.log('Vue changée vers:', viewType);
        };

        // Gestionnaire d'événements
        $(document).on('click', '.view-switch-btn', function(e) {
            e.preventDefault();
            console.log('=== CLIC DÉTECTÉ SUR BOUTON VUE ===');
            
            var viewType = $(this).data('view');
            console.log('Type de vue demandé:', viewType);
            
            window.switchView(viewType);
        });

        // Fonctions de test
        window.testTableView = function() {
            console.log('Test manuel - Passage en vue tableau');
            window.switchView('table');
        };

        window.testGridView = function() {
            console.log('Test manuel - Passage en vue grille');
            window.switchView('grid');
        };

        // Test immédiat
        $(document).ready(function() {
            console.log('=== DOCUMENT READY ===');
            
            setTimeout(function() {
                console.log('=== TEST DES BOUTONS ===');
                var buttons = $('.view-switch-btn');
                console.log('Nombre de boutons trouvés:', buttons.length);
                
                buttons.each(function(index) {
                    console.log('Bouton ' + index + ':', {
                        'data-view': $(this).data('view'),
                        'visible': $(this).is(':visible'),
                        'html': $(this).html()
                    });
                });
            }, 100);
        });
    </script>
</body>
</html>
