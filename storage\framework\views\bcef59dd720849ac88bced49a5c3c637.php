<?php $__env->startSection('styles'); ?>
<link rel="stylesheet" href="<?php echo e(asset('css/vehicule-liste.css')); ?>">
<?php $__env->stopSection(); ?>

<?php $__env->startSection('admin'); ?>
<div class="page-content">
    <!-- En-tête de page avec titre et bouton d'ajout -->
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h4 class="mb-2">Gestion du Parc Automobile</h4>
                <p>Gérez et suivez tous les véhicules de votre parc automobile en un seul endroit</p>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">Tableau de bord</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Parc Automobile</li>
                    </ol>
                </nav>
            </div>
            <div>
                <a href="<?php echo e(route('ajouter.engin')); ?>" class="btn btn-add-vehicle">
                    <i class="bx bx-plus-circle"></i> Ajouter un véhicule
                </a>
            </div>
        </div>
    </div>

    <!-- Statistiques magnifiques du parc automobile -->
    <div class="beautiful-stats-container">
        <!-- Carte 1: Total des véhicules -->
        <div class="beautiful-stat-card total-card" onclick="window.location.href='<?php echo e(route('pageListeVehicule')); ?>'">
            <div class="beautiful-card-header">
                <div class="beautiful-card-info">
                    <div class="beautiful-card-value" data-count="<?php echo e($stats['total'] ?? 11); ?>"><?php echo e($stats['total'] ?? 11); ?></div>
                    <div class="beautiful-card-title">Total Véhicules</div>
                    <div class="beautiful-card-subtitle">Parc automobile DAF</div>
                </div>
                <div class="beautiful-card-icon">
                    <i class="bx bx-car"></i>
                </div>
            </div>
            <div class="beautiful-card-body">
                <div class="beautiful-details">
                    <div class="beautiful-detail-item">
                        <div class="beautiful-detail-left">
                            <div class="beautiful-detail-icon" style="background: var(--gradient-green);">
                                <i class="bx bx-check-circle"></i>
                            </div>
                            <span class="beautiful-detail-text">Bon état</span>
                        </div>
                        <span class="beautiful-detail-value"><?php echo e($stats['bon'] ?? 8); ?></span>
                    </div>
                    <div class="beautiful-detail-item">
                        <div class="beautiful-detail-left">
                            <div class="beautiful-detail-icon" style="background: var(--gradient-orange);">
                                <i class="bx bx-info-circle"></i>
                            </div>
                            <span class="beautiful-detail-text">État passable</span>
                        </div>
                        <span class="beautiful-detail-value"><?php echo e($stats['moyen'] ?? 2); ?></span>
                    </div>
                    <div class="beautiful-detail-item">
                        <div class="beautiful-detail-left">
                            <div class="beautiful-detail-icon" style="background: var(--gradient-red);">
                                <i class="bx bx-error-circle"></i>
                            </div>
                            <span class="beautiful-detail-text">En panne</span>
                        </div>
                        <span class="beautiful-detail-value"><?php echo e($stats['panne'] ?? 1); ?></span>
                    </div>
                </div>
                <div class="beautiful-progress">
                    <div class="beautiful-progress-label">
                        <span>Disponibilité totale</span>
                        <span><?php echo e(round((($stats['bon'] ?? 8) + ($stats['moyen'] ?? 2)) / ($stats['total'] ?? 11) * 100)); ?>%</span>
                    </div>
                    <div class="beautiful-progress-track">
                        <div class="beautiful-progress-fill" style="width: <?php echo e(round((($stats['bon'] ?? 8) + ($stats['moyen'] ?? 2)) / ($stats['total'] ?? 11) * 100)); ?>%; background: var(--gradient-blue);"></div>
                    </div>
                </div>
                <div class="beautiful-card-footer">
                    <div class="beautiful-trend positive">
                        <i class="bx bx-trending-up"></i>
                        +8.3%
                    </div>
                    <div class="beautiful-update">
                        <i class="bx bx-refresh"></i>
                        Actualisé maintenant
                    </div>
                </div>
            </div>
        </div>

        <!-- Carte 2: Véhicules en bon état -->
        <div class="beautiful-stat-card good-card">
            <div class="beautiful-card-header">
                <div class="beautiful-card-info">
                    <div class="beautiful-card-value" data-count="<?php echo e($stats['bon'] ?? 8); ?>"><?php echo e($stats['bon'] ?? 8); ?></div>
                    <div class="beautiful-card-title">Bon État</div>
                    <div class="beautiful-card-subtitle">Véhicules opérationnels</div>
                </div>
                <div class="beautiful-card-icon">
                    <i class="bx bx-check-circle"></i>
                </div>
            </div>
            <div class="beautiful-card-body">
                <div class="beautiful-details">
                    <div class="beautiful-detail-item">
                        <div class="beautiful-detail-left">
                            <div class="beautiful-detail-icon" style="background: var(--gradient-green);">
                                <i class="bx bx-shield-check"></i>
                            </div>
                            <span class="beautiful-detail-text">Contrôle technique OK</span>
                        </div>
                        <span class="beautiful-detail-value"><?php echo e($stats['bon'] ?? 8); ?></span>
                    </div>
                    <div class="beautiful-detail-item">
                        <div class="beautiful-detail-left">
                            <div class="beautiful-detail-icon" style="background: var(--gradient-blue);">
                                <i class="bx bx-gas-pump"></i>
                            </div>
                            <span class="beautiful-detail-text">Carburant suffisant</span>
                        </div>
                        <span class="beautiful-detail-value"><?php echo e($stats['bon'] ?? 8); ?></span>
                    </div>
                    <div class="beautiful-detail-item">
                        <div class="beautiful-detail-left">
                            <div class="beautiful-detail-icon" style="background: var(--gradient-purple);">
                                <i class="bx bx-calendar-check"></i>
                            </div>
                            <span class="beautiful-detail-text">Entretien à jour</span>
                        </div>
                        <span class="beautiful-detail-value"><?php echo e($stats['bon'] ?? 8); ?></span>
                    </div>
                </div>
                <div class="beautiful-progress">
                    <div class="beautiful-progress-label">
                        <span>Disponibilité immédiate</span>
                        <span>100%</span>
                    </div>
                    <div class="beautiful-progress-track">
                        <div class="beautiful-progress-fill" style="width: 100%; background: var(--gradient-green);"></div>
                    </div>
                </div>
                <div class="beautiful-card-footer">
                    <div class="beautiful-trend positive">
                        <i class="bx bx-trending-up"></i>
                        +12.5%
                    </div>
                    <div class="beautiful-update">
                        <i class="bx bx-check"></i>
                        Vérifiés aujourd'hui
                    </div>
                </div>
            </div>
        </div>

        <!-- Carte 3: Véhicules état passable -->
        <div class="beautiful-stat-card fair-card">
            <div class="beautiful-card-header">
                <div class="beautiful-card-info">
                    <div class="beautiful-card-value" data-count="<?php echo e($stats['moyen'] ?? 2); ?>"><?php echo e($stats['moyen'] ?? 2); ?></div>
                    <div class="beautiful-card-title">État Passable</div>
                    <div class="beautiful-card-subtitle">Nécessitent surveillance</div>
                </div>
                <div class="beautiful-card-icon">
                    <i class="bx bx-info-circle"></i>
                </div>
            </div>
            <div class="beautiful-card-body">
                <div class="beautiful-details">
                    <div class="beautiful-detail-item">
                        <div class="beautiful-detail-left">
                            <div class="beautiful-detail-icon" style="background: var(--gradient-orange);">
                                <i class="bx bx-wrench"></i>
                            </div>
                            <span class="beautiful-detail-text">Maintenance préventive</span>
                        </div>
                        <span class="beautiful-detail-value"><?php echo e($stats['moyen'] ?? 2); ?></span>
                    </div>
                    <div class="beautiful-detail-item">
                        <div class="beautiful-detail-left">
                            <div class="beautiful-detail-icon" style="background: var(--gradient-blue);">
                                <i class="bx bx-calendar"></i>
                            </div>
                            <span class="beautiful-detail-text">Révision programmée</span>
                        </div>
                        <span class="beautiful-detail-value">1</span>
                    </div>
                    <div class="beautiful-detail-item">
                        <div class="beautiful-detail-left">
                            <div class="beautiful-detail-icon" style="background: var(--gradient-purple);">
                                <i class="bx bx-time"></i>
                            </div>
                            <span class="beautiful-detail-text">Surveillance renforcée</span>
                        </div>
                        <span class="beautiful-detail-value">1</span>
                    </div>
                </div>
                <div class="beautiful-progress">
                    <div class="beautiful-progress-label">
                        <span>Fiabilité</span>
                        <span>75%</span>
                    </div>
                    <div class="beautiful-progress-track">
                        <div class="beautiful-progress-fill" style="width: 75%; background: var(--gradient-orange);"></div>
                    </div>
                </div>
                <div class="beautiful-card-footer">
                    <div class="beautiful-trend neutral">
                        <i class="bx bx-minus"></i>
                        Stable
                    </div>
                    <div class="beautiful-update">
                        <i class="bx bx-calendar"></i>
                        Contrôle hebdomadaire
                    </div>
                </div>
            </div>
        </div>

        <!-- Carte 4: Véhicules en panne -->
        <div class="beautiful-stat-card bad-card">
            <div class="beautiful-card-header">
                <div class="beautiful-card-info">
                    <div class="beautiful-card-value" data-count="<?php echo e($stats['panne'] ?? 1); ?>"><?php echo e($stats['panne'] ?? 1); ?></div>
                    <div class="beautiful-card-title">En Panne</div>
                    <div class="beautiful-card-subtitle">Intervention requise</div>
                </div>
                <div class="beautiful-card-icon">
                    <i class="bx bx-error-circle"></i>
                </div>
            </div>
            <div class="beautiful-card-body">
                <div class="beautiful-details">
                    <div class="beautiful-detail-item">
                        <div class="beautiful-detail-left">
                            <div class="beautiful-detail-icon" style="background: var(--gradient-red);">
                                <i class="bx bx-error"></i>
                            </div>
                            <span class="beautiful-detail-text">Panne mécanique</span>
                        </div>
                        <span class="beautiful-detail-value"><?php echo e($stats['panne'] ?? 1); ?></span>
                    </div>
                    <div class="beautiful-detail-item">
                        <div class="beautiful-detail-left">
                            <div class="beautiful-detail-icon" style="background: var(--gradient-orange);">
                                <i class="bx bx-cog"></i>
                            </div>
                            <span class="beautiful-detail-text">En réparation</span>
                        </div>
                        <span class="beautiful-detail-value"><?php echo e($stats['panne'] ?? 1); ?></span>
                    </div>
                    <div class="beautiful-detail-item">
                        <div class="beautiful-detail-left">
                            <div class="beautiful-detail-icon" style="background: var(--gradient-blue);">
                                <i class="bx bx-calendar-event"></i>
                            </div>
                            <span class="beautiful-detail-text">Retour estimé</span>
                        </div>
                        <span class="beautiful-detail-value">3j</span>
                    </div>
                </div>
                <div class="beautiful-progress">
                    <div class="beautiful-progress-label">
                        <span>Progression réparation</span>
                        <span>65%</span>
                    </div>
                    <div class="beautiful-progress-track">
                        <div class="beautiful-progress-fill" style="width: 65%; background: var(--gradient-red);"></div>
                    </div>
                </div>
                <div class="beautiful-card-footer">
                    <div class="beautiful-trend negative">
                        <i class="bx bx-trending-down"></i>
                        -9.1%
                    </div>
                    <div class="beautiful-update">
                        <i class="bx bx-wrench"></i>
                        Atelier actif
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- Filtres et recherche -->
    <div class="filters-card">
        <div class="filters-card-body">
            <form method="GET" action="<?php echo e(route('pageListeVehicule')); ?>" id="filterForm">
                <div class="row align-items-end">
                    <div class="col-lg-4 col-md-6 mb-3">
                        <div class="search-box">
                            <i class="bx bx-search"></i>
                            <input type="text" 
                                   class="form-control" 
                                   name="search" 
                                   id="searchInput"
                                   placeholder="Rechercher par immatriculation, marque, etc." 
                                   value="<?php echo e(request('search')); ?>">
                        </div>
                    </div>
                    <div class="col-lg-8 col-md-6 mb-3">
                        <div class="filter-buttons">
                            <!-- Boutons de filtre -->
                            <button type="button" class="btn btn-outline-primary filter-btn" data-filter="all">
                                <i class="bx bx-list-ul"></i> Tous
                            </button>
                            <button type="button" class="btn btn-outline-success filter-btn" data-filter="good">
                                <i class="bx bx-check-circle"></i> Bon état
                            </button>
                            <button type="button" class="btn btn-outline-warning filter-btn" data-filter="fair">
                                <i class="bx bx-error-circle"></i> État moyen
                            </button>
                            <button type="button" class="btn btn-outline-danger filter-btn" data-filter="bad">
                                <i class="bx bx-x-circle"></i> Mauvais état
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Switch vue tableau/grille -->
    <div class="view-controls-card">
        <div class="d-flex justify-content-between align-items-center">
            <div class="view-info">
                <h6 class="mb-1">Liste des véhicules</h6>
                <small class="text-muted"><?php echo e($vehicules->total()); ?> véhicule(s) trouvé(s)</small>
            </div>
            <div class="view-switch">
                <span class="view-switch-label">Mode d'affichage :</span>
                <div class="view-switch-buttons">
                    <button type="button" class="view-switch-btn" data-view="table" title="Vue tableau">
                        <i class="bx bx-table"></i>
                        <span>Tableau</span>
                    </button>
                    <button type="button" class="view-switch-btn active" data-view="grid" title="Vue grille">
                        <i class="bx bx-grid-alt"></i>
                        <span>Grille</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Vue tableau -->
    <div id="tableView" class="d-none">
        <div class="vehicle-table-card">
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Image</th>
                            <th>Véhicule</th>
                            <th>Immatriculation</th>
                            <th>État</th>
                            <th>Département</th>
                            <th>Utilisateur</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if($vehicules->count() > 0): ?>
                            <?php $__currentLoopData = $vehicules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $vehicule): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <td>
                                <div class="vehicle-img">
                                    <?php if($vehicule->image && file_exists(public_path($vehicule->image))): ?>
                                        <img src="<?php echo e(asset($vehicule->image)); ?>"
                                             alt="<?php echo e($vehicule->marque); ?> <?php echo e($vehicule->modele); ?>"
                                             class="vehicle-thumbnail"
                                             onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                        <div class="d-none align-items-center justify-content-center h-100" style="display: none !important;">
                                            <i class="bx bx-car" style="font-size: 2rem; color: #ccc;"></i>
                                        </div>
                                    <?php else: ?>
                                        <div class="d-flex align-items-center justify-content-center h-100">
                                            <i class="bx bx-car" style="font-size: 2rem; color: #ccc;"></i>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <strong><?php echo e($vehicule->marque); ?> <?php echo e($vehicule->modele); ?></strong><br>
                                <small class="text-muted"><?php echo e($vehicule->annee); ?></small>
                            </td>
                            <td>
                                <span class="badge bg-primary"><?php echo e($vehicule->immatriculation); ?></span>
                            </td>
                            <td>
                                <span class="vehicle-status <?php echo e($vehicule->etat); ?>">
                                    <?php if($vehicule->etat == 'good'): ?>
                                        <i class="bx bx-check-circle"></i> Bon état
                                    <?php elseif($vehicule->etat == 'fair'): ?>
                                        <i class="bx bx-error-circle"></i> État moyen
                                    <?php elseif($vehicule->etat == 'warning'): ?>
                                        <i class="bx bx-error-circle"></i> Attention
                                    <?php else: ?>
                                        <i class="bx bx-x-circle"></i> Mauvais état
                                    <?php endif; ?>
                                </span>
                            </td>
                            <td><?php echo e($vehicule->departement->nom_departement ?? 'Non assigné'); ?></td>
                            <td><?php echo e($vehicule->employee ? $vehicule->employee->first_name . ' ' . $vehicule->employee->last_name : 'Non assigné'); ?></td>
                            <td>
                                <div class="btn-group-custom">
                                    <a href="#" class="btn-action-custom btn-view-custom" 
                                       onclick="showVehicleDetails(<?php echo e($vehicule->id); ?>)" title="Voir">
                                        <i class="bx bx-show"></i>
                                    </a>
                                    <a href="<?php echo e(route('editer.engin', $vehicule->id)); ?>"
                                       class="btn-action-custom btn-edit-custom" title="Modifier">
                                        <i class="bx bx-edit"></i>
                                    </a>
                                    <button type="button" class="btn-action-custom btn-delete-custom" 
                                            onclick="confirmDelete(<?php echo e($vehicule->id); ?>)" title="Supprimer">
                                        <i class="bx bx-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php else: ?>
                        <tr>
                            <td colspan="7" class="text-center py-5">
                                <i class="bx bx-car" style="font-size: 3rem; color: var(--gray-400); margin-bottom: 1rem;"></i>
                                <h6 class="text-muted">Aucun véhicule trouvé</h6>
                                <p class="text-muted mb-3">Il n'y a actuellement aucun véhicule dans votre parc automobile.</p>
                                <a href="<?php echo e(route('ajouter.engin')); ?>" class="btn btn-primary btn-sm">
                                    <i class="bx bx-plus-circle"></i> Ajouter le premier véhicule
                                </a>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Vue grille -->
    <div id="gridView" class="grid-view">
        <?php if($vehicules->count() > 0): ?>
            <?php $__currentLoopData = $vehicules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $vehicule): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="vehicle-card" data-status="<?php echo e($vehicule->etat); ?>">
            <div class="vehicle-card-img">
                <?php if($vehicule->image && file_exists(public_path($vehicule->image))): ?>
                    <img src="<?php echo e(asset($vehicule->image)); ?>"
                         alt="<?php echo e($vehicule->marque); ?> <?php echo e($vehicule->modele); ?>"
                         class="vehicle-card-thumbnail"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                    <div class="d-none align-items-center justify-content-center h-100" style="display: none !important;">
                        <i class="bx bx-car" style="font-size: 4rem; color: rgba(255,255,255,0.7);"></i>
                    </div>
                <?php else: ?>
                    <div class="d-flex align-items-center justify-content-center h-100">
                        <i class="bx bx-car" style="font-size: 4rem; color: rgba(255,255,255,0.7);"></i>
                    </div>
                <?php endif; ?>
                
                <div class="vehicle-card-status modern-status-badge
                    <?php if(in_array($vehicule->etat, ['Bon', 'BON', 'bon', 'Bon état', 'BON ETAT', 'Neuf', 'NEUF', 'good'])): ?> excellent
                    <?php elseif(in_array($vehicule->etat, ['Moyen', 'MOYEN', 'moyen', 'Passable', 'PASSABLE', 'passable', 'État moyen', 'ETAT MOYEN', 'fair'])): ?> good
                    <?php elseif(in_array($vehicule->etat, ['Panne', 'PANNE', 'panne', 'En Panne', 'EN PANNE', 'en panne', 'À réparer', 'A REPARER', 'warning'])): ?> warning
                    <?php else: ?> critical
                    <?php endif; ?>">
                    <div class="status-icon">
                        <?php if(in_array($vehicule->etat, ['Bon', 'BON', 'bon', 'Bon état', 'BON ETAT', 'Neuf', 'NEUF', 'good'])): ?>
                            <i class="bx bx-check-circle"></i>
                        <?php elseif(in_array($vehicule->etat, ['Moyen', 'MOYEN', 'moyen', 'Passable', 'PASSABLE', 'passable', 'État moyen', 'ETAT MOYEN', 'fair'])): ?>
                            <i class="bx bx-info-circle"></i>
                        <?php elseif(in_array($vehicule->etat, ['Panne', 'PANNE', 'panne', 'En Panne', 'EN PANNE', 'en panne', 'À réparer', 'A REPARER', 'warning'])): ?>
                            <i class="bx bx-error-circle"></i>
                        <?php else: ?>
                            <i class="bx bx-x-circle"></i>
                        <?php endif; ?>
                    </div>
                    <span class="status-text">
                        <?php if(in_array($vehicule->etat, ['Bon', 'BON', 'bon', 'Bon état', 'BON ETAT', 'Neuf', 'NEUF', 'good'])): ?>
                            Excellent
                        <?php elseif(in_array($vehicule->etat, ['Moyen', 'MOYEN', 'moyen', 'Passable', 'PASSABLE', 'passable', 'État moyen', 'ETAT MOYEN', 'fair'])): ?>
                            Bon état
                        <?php elseif(in_array($vehicule->etat, ['Panne', 'PANNE', 'panne', 'En Panne', 'EN PANNE', 'en panne', 'À réparer', 'A REPARER', 'warning'])): ?>
                            En panne
                        <?php else: ?>
                            Hors service
                        <?php endif; ?>
                    </span>
                    <div class="status-pulse"></div>
                </div>
            </div>
            
            <div class="vehicle-card-body">
                <h5 class="vehicle-card-title"><?php echo e($vehicule->marque); ?> <?php echo e($vehicule->modele); ?></h5>
                <p class="vehicle-card-subtitle"><?php echo e($vehicule->immatriculation); ?> • <?php echo e($vehicule->annee); ?></p>
                
                <!-- Section Informations Principales -->
                <div class="vehicle-card-info">
                    <div class="vehicle-card-info-item">
                        <span class="vehicle-card-info-label">
                            <i class="bx bx-calendar-check"></i>
                            Date d'acquisition
                        </span>
                        <span class="vehicle-card-info-value">
                            <?php echo e($vehicule->date_acquisition ? date('d/m/Y', strtotime($vehicule->date_acquisition)) : 'Non spécifiée'); ?>

                        </span>
                    </div>

                    <div class="vehicle-card-info-item">
                        <span class="vehicle-card-info-label">
                            <i class="bx bx-calendar-star"></i>
                            Date d'affectation
                        </span>
                        <span class="vehicle-card-info-value">
                            <?php echo e($vehicule->date_affectation ? date('d/m/Y', strtotime($vehicule->date_affectation)) : 'Non spécifiée'); ?>

                        </span>
                    </div>

                    <div class="vehicle-card-info-item">
                        <span class="vehicle-card-info-label">
                            <i class="bx bx-money-withdraw"></i>
                            Coût d'acquisition
                        </span>
                        <span class="vehicle-card-info-value">
                            <?php echo e($vehicule->valeur_acquisition ? number_format($vehicule->valeur_acquisition, 0, ',', ' ') . ' FCFA' : 'Non spécifié'); ?>

                        </span>
                    </div>
                </div>

                <!-- Section Informations Techniques -->
                <div class="vehicle-card-tech-info">
                    <div class="vehicle-card-tech-item">
                        <span class="vehicle-card-tech-label">
                            <i class="bx bx-category"></i>
                            Genre
                        </span>
                        <span class="vehicle-card-tech-value"><?php echo e($vehicule->genre ?? 'Non spécifié'); ?></span>
                    </div>

                    <div class="vehicle-card-tech-item">
                        <span class="vehicle-card-tech-label">
                            <i class="bx bx-car"></i>
                            Type
                        </span>
                        <span class="vehicle-card-tech-value"><?php echo e($vehicule->type ?? 'Non spécifié'); ?></span>
                    </div>

                    <div class="vehicle-card-tech-item">
                        <span class="vehicle-card-tech-label">
                            <i class="bx bx-tachometer"></i>
                            Puissance
                        </span>
                        <span class="vehicle-card-tech-value"><?php echo e($vehicule->puissance ?? 'Non spécifiée'); ?></span>
                    </div>

                    <div class="vehicle-card-tech-item">
                        <span class="vehicle-card-tech-label">
                            <i class="bx bx-package"></i>
                            PTC
                        </span>
                        <span class="vehicle-card-tech-value"><?php echo e($vehicule->ptc ?? 'Non spécifié'); ?></span>
                    </div>
                </div>

                <!-- Section Usage et Observations -->
                <?php if($vehicule->usage || $vehicule->observation): ?>
                <div class="vehicle-card-usage-info">
                    <?php if($vehicule->usage): ?>
                    <div class="vehicle-card-usage-item">
                        <span class="vehicle-card-usage-label">
                            <i class="bx bx-target-lock"></i>
                            Usage
                        </span>
                        <span class="vehicle-card-usage-value"><?php echo e($vehicule->usage); ?></span>
                    </div>
                    <?php endif; ?>

                    <?php if($vehicule->observation): ?>
                    <div class="vehicle-card-usage-item full-width">
                        <span class="vehicle-card-usage-label">
                            <i class="bx bx-note"></i>
                            Observations
                        </span>
                        <span class="vehicle-card-usage-value"><?php echo e($vehicule->observation); ?></span>
                    </div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>
            
            <div class="vehicle-card-footer">
                <div class="vehicle-card-department">
                    <?php echo e($vehicule->departement->nom_departement ?? 'Non assigné'); ?>

                </div>

                <?php if($vehicule->employee): ?>
                <div class="vehicle-card-user">
                    <i class="bx bx-user"></i>
                    <?php echo e($vehicule->employee->first_name); ?> <?php echo e($vehicule->employee->last_name); ?>

                </div>
                <?php else: ?>
                <div class="vehicle-card-user">
                    <i class="bx bx-user"></i>
                    <span class="text-muted">Non assigné</span>
                </div>
                <?php endif; ?>
                
                <div class="vehicle-card-actions">
                    <button type="button" class="btn btn-action view" 
                            onclick="showVehicleDetails(<?php echo e($vehicule->id); ?>)" title="Voir les détails">
                        <i class="bx bx-show"></i>
                    </button>
                    <a href="<?php echo e(route('editer.engin', $vehicule->id)); ?>"
                       class="btn btn-action edit" title="Modifier">
                        <i class="bx bx-edit"></i>
                    </a>
                    <button type="button" class="btn btn-action delete" 
                            onclick="confirmDelete(<?php echo e($vehicule->id); ?>)" title="Supprimer">
                        <i class="bx bx-trash"></i>
                    </button>
                </div>
            </div>
        </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php else: ?>
        <div class="no-vehicles-message">
            <div class="text-center py-5">
                <i class="bx bx-car" style="font-size: 4rem; color: var(--gray-400); margin-bottom: 1rem;"></i>
                <h5 class="text-muted">Aucun véhicule trouvé</h5>
                <p class="text-muted">Il n'y a actuellement aucun véhicule dans votre parc automobile.</p>
                <a href="<?php echo e(route('ajouter.engin')); ?>" class="btn btn-primary">
                    <i class="bx bx-plus-circle"></i> Ajouter le premier véhicule
                </a>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- Pagination Améliorée -->
    <?php if($vehicules->hasPages() || $vehicules->total() > 10): ?>
    <div class="modern-pagination-container">
        <div class="pagination-controls">
            <!-- Informations de pagination -->
            <div class="pagination-info-section">
                <div class="pagination-summary">
                    <span class="pagination-text">
                        Affichage de <strong><?php echo e($vehicules->firstItem() ?? 0); ?></strong> à
                        <strong><?php echo e($vehicules->lastItem() ?? 0); ?></strong>
                        sur <strong><?php echo e($vehicules->total()); ?></strong> véhicules
                    </span>
                </div>

                <!-- Sélecteur d'éléments par page -->
                <div class="per-page-selector">
                    <label for="perPageSelect" class="per-page-label">Afficher :</label>
                    <select id="perPageSelect" class="form-select per-page-select" onchange="changePerPage(this.value)">
                        <option value="10" <?php echo e(request('per_page', 10) == 10 ? 'selected' : ''); ?>>10</option>
                        <option value="25" <?php echo e(request('per_page', 10) == 25 ? 'selected' : ''); ?>>25</option>
                        <option value="50" <?php echo e(request('per_page', 10) == 50 ? 'selected' : ''); ?>>50</option>
                        <option value="100" <?php echo e(request('per_page', 10) == 100 ? 'selected' : ''); ?>>100</option>
                    </select>
                    <span class="per-page-suffix">par page</span>
                </div>
            </div>

            <!-- Navigation de pagination -->
            <?php if($vehicules->hasPages()): ?>
            <div class="pagination-navigation">
                <nav aria-label="Navigation des pages">
                    <ul class="modern-pagination">
                        
                        <?php if($vehicules->currentPage() > 1): ?>
                            <li class="page-item">
                                <a class="page-link first-page" href="<?php echo e($vehicules->url(1)); ?>" title="Première page">
                                    <i class="bx bx-chevrons-left"></i>
                                </a>
                            </li>
                        <?php endif; ?>

                        
                        <?php if($vehicules->onFirstPage()): ?>
                            <li class="page-item disabled">
                                <span class="page-link prev-page">
                                    <i class="bx bx-chevron-left"></i>
                                </span>
                            </li>
                        <?php else: ?>
                            <li class="page-item">
                                <a class="page-link prev-page" href="<?php echo e($vehicules->previousPageUrl()); ?>" title="Page précédente">
                                    <i class="bx bx-chevron-left"></i>
                                </a>
                            </li>
                        <?php endif; ?>

                        
                        <?php
                            $start = max(1, $vehicules->currentPage() - 2);
                            $end = min($vehicules->lastPage(), $vehicules->currentPage() + 2);
                        ?>

                        <?php if($start > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="<?php echo e($vehicules->url(1)); ?>">1</a>
                            </li>
                            <?php if($start > 2): ?>
                                <li class="page-item disabled">
                                    <span class="page-link dots">...</span>
                                </li>
                            <?php endif; ?>
                        <?php endif; ?>

                        <?php for($i = $start; $i <= $end; $i++): ?>
                            <?php if($i == $vehicules->currentPage()): ?>
                                <li class="page-item active">
                                    <span class="page-link current-page"><?php echo e($i); ?></span>
                                </li>
                            <?php else: ?>
                                <li class="page-item">
                                    <a class="page-link" href="<?php echo e($vehicules->url($i)); ?>"><?php echo e($i); ?></a>
                                </li>
                            <?php endif; ?>
                        <?php endfor; ?>

                        <?php if($end < $vehicules->lastPage()): ?>
                            <?php if($end < $vehicules->lastPage() - 1): ?>
                                <li class="page-item disabled">
                                    <span class="page-link dots">...</span>
                                </li>
                            <?php endif; ?>
                            <li class="page-item">
                                <a class="page-link" href="<?php echo e($vehicules->url($vehicules->lastPage())); ?>"><?php echo e($vehicules->lastPage()); ?></a>
                            </li>
                        <?php endif; ?>

                        
                        <?php if($vehicules->hasMorePages()): ?>
                            <li class="page-item">
                                <a class="page-link next-page" href="<?php echo e($vehicules->nextPageUrl()); ?>" title="Page suivante">
                                    <i class="bx bx-chevron-right"></i>
                                </a>
                            </li>
                        <?php else: ?>
                            <li class="page-item disabled">
                                <span class="page-link next-page">
                                    <i class="bx bx-chevron-right"></i>
                                </span>
                            </li>
                        <?php endif; ?>

                        
                        <?php if($vehicules->currentPage() < $vehicules->lastPage()): ?>
                            <li class="page-item">
                                <a class="page-link last-page" href="<?php echo e($vehicules->url($vehicules->lastPage())); ?>" title="Dernière page">
                                    <i class="bx bx-chevrons-right"></i>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>

                <!-- Saut de page rapide -->
                <?php if($vehicules->lastPage() > 10): ?>
                <div class="quick-jump">
                    <span class="quick-jump-label">Aller à la page :</span>
                    <input type="number" id="pageJump" class="form-control page-jump-input"
                           min="1" max="<?php echo e($vehicules->lastPage()); ?>"
                           placeholder="<?php echo e($vehicules->currentPage()); ?>">
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="jumpToPage()">
                        <i class="bx bx-right-arrow-alt"></i>
                    </button>
                </div>
                <?php endif; ?>
            </div>
            <?php endif; ?>
        </div>
    </div>
    <?php endif; ?>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
$(document).ready(function() {
    console.log('=== DOM PRÊT ===');
    console.log('Éléments trouvés:');
    console.log('- Boutons vue:', $('.view-switch-btn').length);
    console.log('- Vue grille:', $('#gridView').length);
    console.log('- Vue tableau:', $('#tableView').length);

    // Initialiser immédiatement les vues avant tout autre script
    const savedView = localStorage.getItem('vehiculeViewMode') || 'grid';
    console.log('Vue sauvegardée:', savedView);

    // Forcer l'état initial immédiatement
    if (savedView === 'table') {
        $('#gridView').hide().addClass('d-none');
        $('#tableView').show().removeClass('d-none');
        $('.view-switch-btn[data-view="table"]').addClass('active');
        $('.view-switch-btn[data-view="grid"]').removeClass('active');
    } else {
        $('#tableView').hide().addClass('d-none');
        $('#gridView').show().removeClass('d-none').css('display', 'grid');
        $('.view-switch-btn[data-view="grid"]').addClass('active');
        $('.view-switch-btn[data-view="table"]').removeClass('active');
    }

    // Fonction pour changer de vue
    window.switchView = function(viewType) {
        console.log('=== CHANGEMENT DE VUE ===');
        console.log('Type demandé:', viewType);

        // Mettre à jour les boutons
        $('.view-switch-btn').removeClass('active');
        $(`.view-switch-btn[data-view="${viewType}"]`).addClass('active');
        console.log('Boutons mis à jour');

        if (viewType === 'table') {
            // Activer la vue tableau
            $('#gridView').addClass('d-none').hide();
            $('#tableView').removeClass('d-none').show();
            console.log('✅ Vue tableau activée');
        } else {
            // Activer la vue grille
            $('#tableView').addClass('d-none').hide();
            $('#gridView').removeClass('d-none').css('display', 'grid').show();
            console.log('✅ Vue grille activée');
        }

        localStorage.setItem('vehiculeViewMode', viewType);
        console.log('Vue sauvegardée dans localStorage');
    };

    // Gestionnaire d'événements pour les boutons de vue
    $(document).on('click', '.view-switch-btn', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const viewType = $(this).data('view');
        console.log('🔘 CLIC DÉTECTÉ sur bouton:', viewType);
        console.log('Élément cliqué:', this);

        switchView(viewType);
    });

    // L'initialisation a déjà été faite plus haut

    // Fonctions de pagination
    window.changePerPage = function(perPage) {
        const url = new URL(window.location);
        url.searchParams.set('per_page', perPage);
        url.searchParams.delete('page'); // Reset à la première page
        window.location.href = url.toString();
    };

    window.jumpToPage = function() {
        const pageInput = document.getElementById('pageJump');
        const pageNumber = parseInt(pageInput.value);
        const maxPage = <?php echo e($vehicules->lastPage()); ?>;

        if (pageNumber && pageNumber >= 1 && pageNumber <= maxPage) {
            const url = new URL(window.location);
            url.searchParams.set('page', pageNumber);
            window.location.href = url.toString();
        } else {
            Swal.fire({
                icon: 'warning',
                title: 'Page invalide',
                text: `Veuillez entrer un numéro de page entre 1 et ${maxPage}`,
                confirmButtonColor: '#667eea'
            });
        }
    };

    // Permettre l'utilisation de la touche Entrée pour le saut de page
    document.getElementById('pageJump')?.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            jumpToPage();
        }
    });

    // Animation d'apparition des cartes (désactivée temporairement)
    function animateCards() {
        // Animation désactivée pour éviter les conflits d'affichage
        // const cards = document.querySelectorAll('.vehicle-card');
        // cards.forEach((card, index) => {
        //     card.style.animationDelay = `${index * 0.1}s`;
        //     card.classList.add('fade-in-up');
        // });
    }

    // Lancer l'animation au chargement
    // animateCards();

    // Améliorer l'expérience utilisateur avec des indicateurs de chargement
    window.showLoadingIndicator = function() {
        const indicator = document.createElement('div');
        indicator.className = 'loading-indicator';
        indicator.innerHTML = `
            <div class="loading-spinner"></div>
            <span>Chargement...</span>
        `;
        document.body.appendChild(indicator);
    };

    window.hideLoadingIndicator = function() {
        const indicator = document.querySelector('.loading-indicator');
        if (indicator) {
            indicator.remove();
        }
    };

    // Filtres
    $('.filter-btn').on('click', function() {
        const filter = $(this).data('filter');
        console.log('Filtre appliqué:', filter);

        $('.filter-btn').removeClass('active');
        $(this).addClass('active');

        if (filter === 'all') {
            $('.vehicle-card, .table tbody tr').show();
        } else {
            $('.vehicle-card').hide();
            $('.table tbody tr').hide();
            $(`.vehicle-card[data-status="${filter}"]`).show();
            $(`.table tbody tr[data-status="${filter}"]`).show();
        }
    });

    // Recherche
    $('#searchInput').on('input', function() {
        const searchTerm = $(this).val().toLowerCase();
        console.log('Recherche:', searchTerm);

        $('.vehicle-card').each(function() {
            const cardText = $(this).text().toLowerCase();
            if (cardText.includes(searchTerm)) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });

        $('.table tbody tr').each(function() {
            const rowText = $(this).text().toLowerCase();
            if (rowText.includes(searchTerm)) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    });

    // Test des boutons après chargement
    setTimeout(function() {
        console.log('=== TEST FINAL ===');
        console.log('Boutons actifs:', $('.view-switch-btn.active').length);
        console.log('Vue grille visible:', $('#gridView').is(':visible'));
        console.log('Vue tableau visible:', $('#tableView').is(':visible'));
    }, 1000);
});

// Fonction pour afficher les détails d'un véhicule
function showVehicleDetails(vehicleId) {
    console.log('Affichage des détails pour le véhicule:', vehicleId);
    // TODO: Implémenter l'affichage des détails
}

// Fonction pour confirmer la suppression
function confirmDelete(vehicleId) {
    Swal.fire({
        title: 'Êtes-vous sûr ?',
        text: "Cette action ne peut pas être annulée !",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Oui, supprimer !',
        cancelButtonText: 'Annuler'
    }).then((result) => {
        if (result.isConfirmed) {
            console.log('Suppression du véhicule:', vehicleId);
            // TODO: Implémenter la suppression
        }
    });
}

// Animation de comptage pour les cartes statistiques
document.addEventListener('DOMContentLoaded', function() {
    const statValues = document.querySelectorAll('.stat-card-value[data-count]');

    statValues.forEach(element => {
        const targetValue = parseInt(element.getAttribute('data-count'));
        const duration = 2000; // 2 secondes
        const startTime = performance.now();

        function updateCount(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // Fonction d'easing pour un effet plus naturel
            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const currentValue = Math.floor(targetValue * easeOutQuart);

            element.textContent = currentValue;

            if (progress < 1) {
                requestAnimationFrame(updateCount);
            } else {
                element.textContent = targetValue;
            }
        }

        // Démarrer l'animation avec un délai
        setTimeout(() => {
            requestAnimationFrame(updateCount);
        }, 500);
    });
});
</script>
<?php $__env->stopSection(); ?>

<!-- CSS pour les cartes magnifiques -->
<link href="<?php echo e(asset('css/vehicule-cards-beautiful.css')); ?>" rel="stylesheet">

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Détection de la taille d'écran
    const isMobile = window.innerWidth <= 768;
    const isTablet = window.innerWidth > 768 && window.innerWidth <= 1024;

    // Animation de comptage pour les valeurs (adaptée selon l'écran)
    const statValues = document.querySelectorAll('.beautiful-card-value[data-count]');

    statValues.forEach((element, index) => {
        const targetValue = parseInt(element.getAttribute('data-count'));
        const duration = isMobile ? 1500 : 2000; // Plus rapide sur mobile
        const delay = isMobile ? index * 200 : index * 400; // Délais réduits sur mobile

        setTimeout(() => {
            animateValue(element, 0, targetValue, duration);
        }, delay);
    });

    function animateValue(element, start, end, duration) {
        const startTime = performance.now();

        function updateValue(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // Easing function pour un effet plus naturel
            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const currentValue = Math.floor(start + (end - start) * easeOutQuart);

            element.textContent = currentValue;

            if (progress < 1) {
                requestAnimationFrame(updateValue);
            } else {
                element.textContent = end;
            }
        }

        requestAnimationFrame(updateValue);
    }

    // Animation des barres de progression magnifiques (adaptée selon l'écran)
    setTimeout(() => {
        const progressBars = document.querySelectorAll('.beautiful-progress-fill');
        progressBars.forEach((bar, index) => {
            setTimeout(() => {
                const width = bar.style.width;
                bar.style.width = '0%';
                const duration = isMobile ? '1.5s' : '2s'; // Plus rapide sur mobile
                bar.style.transition = `width ${duration} cubic-bezier(0.4, 0, 0.2, 1)`;
                setTimeout(() => {
                    bar.style.width = width;
                }, isMobile ? 100 : 150);
            }, index * (isMobile ? 200 : 300));
        });
    }, isMobile ? 800 : 1000);

    // Effet de hover magnifique pour les cartes (adapté selon l'écran)
    const cards = document.querySelectorAll('.beautiful-stat-card');
    cards.forEach(card => {
        // Événements de hover seulement sur desktop
        if (!isMobile) {
            card.addEventListener('mouseenter', function() {
                this.style.cursor = 'pointer';
                // Animation des icônes de détail
                const detailIcons = this.querySelectorAll('.beautiful-detail-icon');
                detailIcons.forEach((icon, index) => {
                    setTimeout(() => {
                        icon.style.transform = 'scale(1.15) rotate(10deg)';
                        icon.style.transition = 'transform 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
                    }, index * 75);
                });

                // Animation de l'icône principale
                const mainIcon = this.querySelector('.beautiful-card-icon');
                if (mainIcon) {
                    mainIcon.style.transform = 'rotate(360deg) scale(1.1)';
                }
            });

            card.addEventListener('mouseleave', function() {
                const detailIcons = this.querySelectorAll('.beautiful-detail-icon');
                detailIcons.forEach(icon => {
                    icon.style.transform = 'scale(1) rotate(0deg)';
                });

                const mainIcon = this.querySelector('.beautiful-card-icon');
                if (mainIcon) {
                    mainIcon.style.transform = 'rotate(0deg) scale(1)';
                }
            });
        }

        // Événements tactiles pour mobile
        if (isMobile) {
            card.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.98)';
                this.style.transition = 'transform 0.1s ease';
            });

            card.addEventListener('touchend', function() {
                this.style.transform = 'scale(1)';
            });
        }
    });

    // Interactions pour les éléments de détail (adaptées selon l'écran)
    const detailItems = document.querySelectorAll('.beautiful-detail-item');
    detailItems.forEach(item => {
        if (!isMobile) {
            // Interactions hover pour desktop
            item.addEventListener('mouseenter', function() {
                this.style.transform = 'translateX(6px) scale(1.02)';
                this.style.boxShadow = '0 8px 25px rgba(0,0,0,0.15)';
                this.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
            });

            item.addEventListener('mouseleave', function() {
                this.style.transform = 'translateX(0) scale(1)';
                this.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';
            });
        } else {
            // Interactions tactiles pour mobile
            item.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.97)';
                this.style.transition = 'transform 0.1s ease';
            });

            item.addEventListener('touchend', function() {
                this.style.transform = 'scale(1)';
            });
        }
    });

    // Gestion du redimensionnement de la fenêtre
    window.addEventListener('resize', function() {
        // Réinitialiser les styles si on change de mobile à desktop
        const newIsMobile = window.innerWidth <= 768;
        if (newIsMobile !== isMobile) {
            location.reload(); // Recharger pour appliquer les bonnes interactions
        }
    });
});
</script>

<?php echo $__env->make('admin.admin_dashboard', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\GestionParkAutoFinal_ok\resources\views/vehicule/liste.blade.php ENDPATH**/ ?>