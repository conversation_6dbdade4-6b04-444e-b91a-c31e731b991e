// Dashboard Cards Animations - Scripts pour les animations des cartes
document.addEventListener('DOMContentLoaded', function() {
    // Initialisation des animations
    initCardAnimations();
    initCountUpAnimations();
    initHoverEffects();
    initParallaxEffects();
});

// Animation des cartes au chargement
function initCardAnimations() {
    const cards = document.querySelectorAll('.stat-card');
    
    // Observer pour déclencher les animations quand les cartes entrent dans la vue
    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });

    cards.forEach((card, index) => {
        // Animation d'entrée décalée
        card.style.opacity = '0';
        card.style.transform = 'translateY(50px)';
        card.style.transition = `all 0.8s cubic-bezier(0.4, 0, 0.2, 1) ${index * 0.1}s`;
        
        observer.observe(card);
    });
}

// Animation de comptage des valeurs
function initCountUpAnimations() {
    const statValues = document.querySelectorAll('.stat-value[data-count]');
    
    statValues.forEach(element => {
        const targetValue = parseInt(element.getAttribute('data-count'));
        const duration = 2000; // 2 secondes
        const startTime = performance.now();
        
        function updateCount(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // Fonction d'easing pour un effet plus naturel
            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const currentValue = Math.floor(targetValue * easeOutQuart);
            
            element.textContent = currentValue;
            
            if (progress < 1) {
                requestAnimationFrame(updateCount);
            } else {
                element.textContent = targetValue;
            }
        }
        
        // Démarrer l'animation avec un délai
        setTimeout(() => {
            requestAnimationFrame(updateCount);
        }, 500);
    });
}

// Effets de survol avancés
function initHoverEffects() {
    const cards = document.querySelectorAll('.stat-card');
    
    cards.forEach(card => {
        const icon = card.querySelector('.stat-icon');
        const trendIndicator = card.querySelector('.trend-indicator');
        
        card.addEventListener('mouseenter', function() {
            // Animation de l'icône
            if (icon) {
                icon.style.transform = 'scale(1.1) rotate(5deg)';
                icon.style.boxShadow = '0 12px 40px rgba(0,0,0,0.3)';
            }
            
            // Animation de l'indicateur de tendance
            if (trendIndicator) {
                trendIndicator.style.transform = 'scale(1.1)';
                trendIndicator.style.boxShadow = '0 8px 25px rgba(0,0,0,0.2)';
            }
            
            // Effet de particules
            createParticleEffect(card);
        });
        
        card.addEventListener('mouseleave', function() {
            // Retour à l'état normal
            if (icon) {
                icon.style.transform = 'scale(1) rotate(0deg)';
                icon.style.boxShadow = '0 8px 32px rgba(0,0,0,0.2)';
            }
            
            if (trendIndicator) {
                trendIndicator.style.transform = 'scale(1)';
                trendIndicator.style.boxShadow = '0 4px 20px rgba(0,0,0,0.3)';
            }
        });
    });
}

// Effet de particules au survol
function createParticleEffect(card) {
    const particleCount = 6;
    
    for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.style.cssText = `
            position: absolute;
            width: 4px;
            height: 4px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 50%;
            pointer-events: none;
            z-index: 1000;
        `;
        
        const rect = card.getBoundingClientRect();
        const x = rect.left + Math.random() * rect.width;
        const y = rect.top + Math.random() * rect.height;
        
        particle.style.left = x + 'px';
        particle.style.top = y + 'px';
        
        document.body.appendChild(particle);
        
        // Animation de la particule
        const animation = particle.animate([
            {
                transform: 'translate(0, 0) scale(1)',
                opacity: 1
            },
            {
                transform: `translate(${(Math.random() - 0.5) * 100}px, ${(Math.random() - 0.5) * 100}px) scale(0)`,
                opacity: 0
            }
        ], {
            duration: 1000,
            easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
        });
        
        animation.onfinish = () => {
            particle.remove();
        };
    }
}

// Effets de parallaxe subtils
function initParallaxEffects() {
    const cards = document.querySelectorAll('.stat-card');
    
    document.addEventListener('mousemove', function(e) {
        const mouseX = e.clientX / window.innerWidth;
        const mouseY = e.clientY / window.innerHeight;
        
        cards.forEach((card, index) => {
            const icon = card.querySelector('.stat-icon');
            if (icon) {
                const moveX = (mouseX - 0.5) * 10 * (index % 2 === 0 ? 1 : -1);
                const moveY = (mouseY - 0.5) * 10 * (index % 2 === 0 ? 1 : -1);
                
                icon.style.transform = `translate(${moveX}px, ${moveY}px)`;
            }
        });
    });
}

// Animation de pulsation pour les indicateurs de tendance
function initTrendPulse() {
    const trendIndicators = document.querySelectorAll('.trend-indicator');
    
    trendIndicators.forEach(indicator => {
        setInterval(() => {
            indicator.style.transform = 'scale(1.05)';
            setTimeout(() => {
                indicator.style.transform = 'scale(1)';
            }, 200);
        }, 3000);
    });
}

// Effet de brillance périodique
function initShineEffect() {
    const cards = document.querySelectorAll('.stat-card');
    
    cards.forEach((card, index) => {
        setInterval(() => {
            card.style.animation = 'none';
            setTimeout(() => {
                card.style.animation = 'shine 0.6s ease-in-out';
            }, 10);
        }, 8000 + (index * 2000)); // Décalage pour chaque carte
    });
}

// Animation de rotation des icônes
function initIconRotation() {
    const icons = document.querySelectorAll('.stat-icon i');
    
    icons.forEach(icon => {
        icon.addEventListener('mouseenter', function() {
            this.style.animation = 'rotateIcon 0.6s ease-in-out';
        });
        
        icon.addEventListener('animationend', function() {
            this.style.animation = '';
        });
    });
}

// Ajout des keyframes CSS dynamiquement
function addDynamicStyles() {
    const style = document.createElement('style');
    style.textContent = `
        @keyframes rotateIcon {
            0% { transform: rotate(0deg) scale(1); }
            50% { transform: rotate(180deg) scale(1.2); }
            100% { transform: rotate(360deg) scale(1); }
        }
        
        @keyframes shine {
            0% { box-shadow: 0 10px 40px rgba(0,0,0,0.1); }
            50% { box-shadow: 0 20px 60px rgba(102, 126, 234, 0.3); }
            100% { box-shadow: 0 10px 40px rgba(0,0,0,0.1); }
        }
    `;
    document.head.appendChild(style);
}

// Initialisation complète
function initAllEffects() {
    addDynamicStyles();
    initTrendPulse();
    initShineEffect();
    initIconRotation();
}

// Démarrer tous les effets après un court délai
setTimeout(initAllEffects, 1000);
