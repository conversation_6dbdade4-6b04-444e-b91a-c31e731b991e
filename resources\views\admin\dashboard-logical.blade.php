@extends('admin.admin_dashboard')

@section('admin')
<div class="page-content">
    <!-- En-tête de la page -->
    <div class="page-header">
        <div class="d-flex align-items-center justify-content-between">
            <div>
                <h4 class="mb-2">Dashboard - Design Logique</h4>
                <p class="mb-0">Vue d'ensemble du parc automobile avec un design plus logique et attrayant</p>
            </div>
            <div class="d-flex gap-3">
                <button class="btn btn-outline-light">
                    <i class='bx bx-refresh'></i> Actualiser
                </button>
                <button class="btn btn-light">
                    <i class='bx bx-export'></i> Exporter
                </button>
            </div>
        </div>
    </div>

    <!-- Statistiques principales avec design logique -->
    <div class="dashboard-stats-container">
        
        <!-- Carte 1: Total des véhicules -->
        <div class="stat-card vehicles-card" onclick="window.location.href='#'">
            <div class="stat-card-header">
                <div class="stat-card-info">
                    <div class="stat-card-value" data-count="{{ $totalVehicules ?? 11 }}">{{ $totalVehicules ?? 11 }}</div>
                    <div class="stat-card-title">Total des véhicules</div>
                </div>
                <div class="stat-card-icon">
                    <i class="bx bx-car"></i>
                </div>
            </div>
            <div class="stat-card-body">
                <div class="stat-card-details">
                    <div class="detail-item success">
                        <i class="bx bx-check-circle"></i>
                        <span>{{ $vehiculesOperationnels ?? 10 }} opérationnels</span>
                    </div>
                    <div class="detail-item warning">
                        <i class="bx bx-wrench"></i>
                        <span>{{ $vehiculesEnMaintenance ?? 1 }} en maintenance</span>
                    </div>
                </div>
                <div class="stat-card-footer">
                    <div class="stat-card-trend up">
                        <i class="bx bx-trending-up"></i>
                        +2.9%
                    </div>
                    <span class="stat-card-context">ce mois</span>
                </div>
            </div>
        </div>

        <!-- Carte 2: Véhicules opérationnels -->
        <div class="stat-card operational-card" onclick="window.location.href='#'">
            <div class="stat-card-header">
                <div class="stat-card-info">
                    <div class="stat-card-value" data-count="{{ $vehiculesOperationnels ?? 8 }}">{{ $vehiculesOperationnels ?? 8 }}</div>
                    <div class="stat-card-title">En bon état</div>
                </div>
                <div class="stat-card-icon">
                    <i class="bx bx-check-circle"></i>
                </div>
            </div>
            <div class="stat-card-body">
                <div class="stat-card-details">
                    <div class="detail-item success">
                        <i class="bx bx-shield-check"></i>
                        <span>Prêts à l'utilisation</span>
                    </div>
                    <div class="detail-item neutral">
                        <i class="bx bx-time"></i>
                        <span>Dernière vérification: aujourd'hui</span>
                    </div>
                </div>
                <div class="stat-card-footer">
                    <div class="stat-card-trend up">
                        <i class="bx bx-trending-up"></i>
                        +5.2%
                    </div>
                    <span class="stat-card-context">ce mois</span>
                </div>
            </div>
        </div>

        <!-- Carte 3: Véhicules en maintenance -->
        <div class="stat-card maintenance-card" onclick="window.location.href='#'">
            <div class="stat-card-header">
                <div class="stat-card-info">
                    <div class="stat-card-value" data-count="{{ $vehiculesEnMaintenance ?? 1 }}">{{ $vehiculesEnMaintenance ?? 1 }}</div>
                    <div class="stat-card-title">En maintenance</div>
                </div>
                <div class="stat-card-icon">
                    <i class="bx bx-wrench"></i>
                </div>
            </div>
            <div class="stat-card-body">
                <div class="stat-card-details">
                    <div class="detail-item warning">
                        <i class="bx bx-time-five"></i>
                        <span>Maintenance préventive</span>
                    </div>
                    <div class="detail-item neutral">
                        <i class="bx bx-calendar"></i>
                        <span>Retour prévu: 3 jours</span>
                    </div>
                </div>
                <div class="stat-card-footer">
                    <div class="stat-card-trend down">
                        <i class="bx bx-trending-down"></i>
                        -2.1%
                    </div>
                    <span class="stat-card-context">ce mois</span>
                </div>
            </div>
        </div>

        <!-- Carte 4: Véhicules hors service -->
        <div class="stat-card critical-card" onclick="window.location.href='#'">
            <div class="stat-card-header">
                <div class="stat-card-info">
                    <div class="stat-card-value" data-count="0">0</div>
                    <div class="stat-card-title">Hors service</div>
                </div>
                <div class="stat-card-icon">
                    <i class="bx bx-x-circle"></i>
                </div>
            </div>
            <div class="stat-card-body">
                <div class="stat-card-details">
                    <div class="detail-item success">
                        <i class="bx bx-check"></i>
                        <span>Aucun véhicule critique</span>
                    </div>
                    <div class="detail-item neutral">
                        <i class="bx bx-shield"></i>
                        <span>Flotte en bon état</span>
                    </div>
                </div>
                <div class="stat-card-footer">
                    <div class="stat-card-trend neutral">
                        <i class="bx bx-minus"></i>
                        0%
                    </div>
                    <span class="stat-card-context">ce mois</span>
                </div>
            </div>
        </div>

    </div>

    <!-- Section de comparaison -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Comparaison des Designs</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">✅ Avantages du nouveau design :</h6>
                            <ul class="list-unstyled">
                                <li><i class="bx bx-check text-success me-2"></i>Hiérarchie visuelle claire</li>
                                <li><i class="bx bx-check text-success me-2"></i>Couleurs logiques et cohérentes</li>
                                <li><i class="bx bx-check text-success me-2"></i>Informations contextuelles utiles</li>
                                <li><i class="bx bx-check text-success me-2"></i>Design minimaliste et moderne</li>
                                <li><i class="bx bx-check text-success me-2"></i>Meilleure lisibilité</li>
                                <li><i class="bx bx-check text-success me-2"></i>Interactions intuitives</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-warning">🎯 Améliorations apportées :</h6>
                            <ul class="list-unstyled">
                                <li><i class="bx bx-star text-warning me-2"></i>Système de couleurs sémantique</li>
                                <li><i class="bx bx-star text-warning me-2"></i>Espacement et proportions optimisés</li>
                                <li><i class="bx bx-star text-warning me-2"></i>Détails contextuels pertinents</li>
                                <li><i class="bx bx-star text-warning me-2"></i>Animations subtiles et fluides</li>
                                <li><i class="bx bx-star text-warning me-2"></i>Responsive design amélioré</li>
                                <li><i class="bx bx-star text-warning me-2"></i>Accessibilité renforcée</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>

<!-- Styles spécifiques pour cette page -->
<link href="{{ asset('css/dashboard-cards-logical.css') }}" rel="stylesheet">

<style>
/* Styles additionnels pour les détails des cartes */
.stat-card-details {
    margin-bottom: 1rem;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.detail-item.success {
    color: var(--color-success-dark);
}

.detail-item.warning {
    color: var(--color-warning-dark);
}

.detail-item.neutral {
    color: var(--color-gray-600);
}

.detail-item i {
    font-size: 1rem;
    opacity: 0.8;
}

/* Amélioration de la structure des cartes */
.stat-card-info {
    flex: 1;
}

.stat-card-header {
    padding: 1.5rem 1.5rem 1rem;
}

.stat-card-body {
    padding: 0 1.5rem 1.5rem;
}

/* Effet de clic */
.stat-card {
    cursor: pointer;
    user-select: none;
}

.stat-card:active {
    transform: translateY(-2px) scale(0.98);
}

/* Animation de chargement pour les valeurs */
.stat-card-value {
    transition: all 0.3s ease;
}

.stat-card-value.loading {
    opacity: 0.5;
    transform: scale(0.95);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animation de comptage pour les valeurs
    const statValues = document.querySelectorAll('.stat-card-value[data-count]');
    
    statValues.forEach((element, index) => {
        const targetValue = parseInt(element.getAttribute('data-count'));
        const duration = 1500;
        const delay = index * 200;
        
        setTimeout(() => {
            animateValue(element, 0, targetValue, duration);
        }, delay);
    });
    
    function animateValue(element, start, end, duration) {
        const startTime = performance.now();
        
        function updateValue(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // Easing function pour un effet plus naturel
            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const currentValue = Math.floor(start + (end - start) * easeOutQuart);
            
            element.textContent = currentValue;
            
            if (progress < 1) {
                requestAnimationFrame(updateValue);
            } else {
                element.textContent = end;
            }
        }
        
        requestAnimationFrame(updateValue);
    }
    
    // Effet de hover amélioré
    const cards = document.querySelectorAll('.stat-card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.cursor = 'pointer';
        });
    });
});
</script>

@endsection
