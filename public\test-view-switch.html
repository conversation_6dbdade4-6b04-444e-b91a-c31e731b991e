<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test des boutons de vue</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href='https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css' rel='stylesheet'>
    <style>
        /* Switch vue tableau/grille */
        .view-switch {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }

        .view-switch-label {
            font-size: 0.875rem;
            color: #6b7280;
            margin-right: 0.5rem;
        }

        .view-switch-buttons {
            display: flex;
            border-radius: 0.25rem;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }

        .view-switch-btn {
            padding: 0.5rem;
            background-color: white;
            border: 1px solid #d1d5db;
            color: #6b7280;
            transition: all 0.2s ease;
            cursor: pointer;
            position: relative;
            z-index: 10;
        }

        .view-switch-btn:first-child {
            border-radius: 0.25rem 0 0 0.25rem;
        }

        .view-switch-btn:last-child {
            border-radius: 0 0.25rem 0.25rem 0;
        }

        .view-switch-btn:hover {
            background-color: #f8f9fa;
            border-color: #4f46e5;
            color: #4f46e5;
        }

        .view-switch-btn.active {
            background-color: #4f46e5;
            color: white;
            border-color: #4f46e5;
        }

        .view-switch-btn.active:hover {
            background-color: #4f46e5;
            opacity: 0.9;
        }

        .test-content {
            margin: 2rem 0;
            padding: 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 0.5rem;
        }

        #gridView {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 1rem;
        }

        .grid-item {
            background: #f3f4f6;
            padding: 1rem;
            border-radius: 0.5rem;
            text-align: center;
        }

        #tableView {
            display: block;
        }

        .d-none {
            display: none !important;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>Test des boutons de changement de vue</h1>
        
        <!-- Switch vue tableau/grille -->
        <div class="view-switch">
            <span class="view-switch-label">Vue :</span>
            <div class="view-switch-buttons">
                <button type="button" class="view-switch-btn" data-view="table" title="Vue tableau">
                    <i class="bx bx-table"></i>
                </button>
                <button type="button" class="view-switch-btn active" data-view="grid" title="Vue grille">
                    <i class="bx bx-grid-alt"></i>
                </button>
            </div>
        </div>

        <!-- Vue grille -->
        <div id="gridView" class="test-content">
            <h3>Vue Grille</h3>
            <div class="grid-item">Élément 1</div>
            <div class="grid-item">Élément 2</div>
            <div class="grid-item">Élément 3</div>
            <div class="grid-item">Élément 4</div>
        </div>

        <!-- Vue tableau -->
        <div id="tableView" class="test-content d-none">
            <h3>Vue Tableau</h3>
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Nom</th>
                        <th>Description</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td>Élément 1</td>
                        <td>Description de l'élément 1</td>
                    </tr>
                    <tr>
                        <td>2</td>
                        <td>Élément 2</td>
                        <td>Description de l'élément 2</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="alert alert-info">
            <h5>Instructions de test :</h5>
            <ul>
                <li>Cliquez sur le bouton avec l'icône de tableau pour afficher la vue tableau</li>
                <li>Cliquez sur le bouton avec l'icône de grille pour afficher la vue grille</li>
                <li>Ouvrez la console pour voir les logs de débogage</li>
            </ul>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script>
        $(document).ready(function() {
            console.log('Test des boutons de vue initialisé');
            
            // Fonction de changement de vue
            function switchView(viewType) {
                console.log('=== CHANGEMENT DE VUE ===');
                console.log('Type de vue:', viewType);

                // Mettre à jour les boutons
                $('.view-switch-btn').removeClass('active');
                $('.view-switch-btn[data-view="' + viewType + '"]').addClass('active');

                if (viewType === 'table') {
                    // Afficher la vue tableau
                    $('#gridView').addClass('d-none');
                    $('#tableView').removeClass('d-none');
                    console.log('Vue tableau activée');
                } else {
                    // Afficher la vue grille
                    $('#tableView').addClass('d-none');
                    $('#gridView').removeClass('d-none');
                    console.log('Vue grille activée');
                }

                console.log('Vue changée vers:', viewType);
            }

            // Gestionnaire d'événements pour les boutons de vue
            $(document).on('click', '.view-switch-btn', function(e) {
                e.preventDefault();
                
                var viewType = $(this).data('view');
                console.log('Bouton cliqué - Type:', viewType);

                // Appeler la fonction de changement de vue
                switchView(viewType);
            });

            // Fonctions de test globales
            window.testTableView = function() {
                console.log('Test manuel - Passage en vue tableau');
                switchView('table');
            };
            
            window.testGridView = function() {
                console.log('Test manuel - Passage en vue grille');
                switchView('grid');
            };

            console.log('Fonctions de test disponibles: testTableView() et testGridView()');
        });
    </script>
</body>
</html>
