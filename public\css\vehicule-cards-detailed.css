/* Design Détaillé pour les Cartes Véhicules */
:root {
    /* Couleurs thématiques pour les états des véhicules */
    --vehicle-total: #3b82f6;
    --vehicle-total-light: #dbeafe;
    --vehicle-total-gradient: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
    
    --vehicle-operational: #10b981;
    --vehicle-operational-light: #d1fae5;
    --vehicle-operational-gradient: linear-gradient(135deg, #10b981 0%, #047857 100%);
    
    --vehicle-maintenance: #f59e0b;
    --vehicle-maintenance-light: #fef3c7;
    --vehicle-maintenance-gradient: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    
    --vehicle-critical: #ef4444;
    --vehicle-critical-light: #fecaca;
    --vehicle-critical-gradient: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    
    /* Couleurs neutres */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    /* Espacements */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    
    /* Effets */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 8px 25px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 12px 40px rgba(0, 0, 0, 0.12);
    
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 20px;
    
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Container principal */
.vehicle-stats-container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--space-4);
    padding: var(--space-8) 0;
}

/* Carte véhicule principale */
.vehicle-stat-card {
    background: white;
    border-radius: var(--radius-xl);
    border: 1px solid var(--gray-100);
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    overflow: hidden;
    position: relative;
    min-height: 260px;
    max-height: 260px;
    display: flex;
    flex-direction: column;
    cursor: pointer;
}

.vehicle-stat-card:hover {
    transform: translateY(-6px);
    box-shadow: var(--shadow-xl);
    border-color: var(--gray-200);
}

/* En-tête avec indicateur de statut */
.vehicle-card-header {
    padding: var(--space-4);
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    position: relative;
    background: linear-gradient(135deg, var(--gray-50) 0%, white 100%);
}

/* Indicateur de couleur latéral */
.vehicle-stat-card::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 5px;
    background: var(--vehicle-total);
    transition: var(--transition);
}

.vehicle-stat-card:hover::before {
    width: 8px;
}

/* Icône principale */
.vehicle-card-icon {
    width: 45px;
    height: 45px;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.4rem;
    color: white;
    background: var(--vehicle-total-gradient);
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    position: relative;
}

.vehicle-card-icon::after {
    content: '';
    position: absolute;
    inset: -2px;
    border-radius: var(--radius-lg);
    background: var(--vehicle-total-gradient);
    z-index: -1;
    opacity: 0;
    transition: var(--transition);
}

.vehicle-stat-card:hover .vehicle-card-icon::after {
    opacity: 0.2;
}

/* Informations principales */
.vehicle-card-info {
    flex: 1;
}

.vehicle-card-value {
    font-size: 2.2rem;
    font-weight: 800;
    color: var(--gray-900);
    line-height: 1;
    margin-bottom: var(--space-1);
    letter-spacing: -0.02em;
}

.vehicle-card-title {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--gray-600);
    text-transform: uppercase;
    letter-spacing: 0.1em;
    margin-bottom: var(--space-1);
}

.vehicle-card-subtitle {
    font-size: 0.65rem;
    color: var(--gray-500);
    font-weight: 500;
}

/* Corps de la carte avec détails */
.vehicle-card-body {
    padding: 0 var(--space-4) var(--space-4);
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* Section des détails d'état */
.vehicle-state-details {
    margin-bottom: var(--space-3);
}

.state-detail-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-2) var(--space-3);
    margin-bottom: var(--space-1);
    border-radius: var(--radius-sm);
    background: var(--gray-50);
    border: 1px solid var(--gray-100);
    transition: var(--transition);
}

.state-detail-item:hover {
    background: var(--gray-100);
    transform: translateX(4px);
}

.state-detail-left {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.state-detail-icon {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.65rem;
    color: white;
    background: var(--gray-400);
}

.state-detail-text {
    font-size: 0.7rem;
    font-weight: 500;
    color: var(--gray-700);
}

.state-detail-value {
    font-size: 0.7rem;
    font-weight: 600;
    color: var(--gray-900);
    padding: var(--space-1) var(--space-2);
    background: white;
    border-radius: var(--radius-sm);
    border: 1px solid var(--gray-200);
}

/* Barre de progression */
.vehicle-progress-bar {
    margin-bottom: var(--space-3);
}

.progress-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-1);
    font-size: 0.65rem;
    font-weight: 600;
    color: var(--gray-600);
}

.progress-track {
    height: 6px;
    background: var(--gray-200);
    border-radius: 3px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: var(--vehicle-total-gradient);
    border-radius: 3px;
    transition: width 1s ease-out;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Pied de carte */
.vehicle-card-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: var(--space-4);
    border-top: 1px solid var(--gray-100);
    margin-top: auto;
}

.vehicle-trend {
    display: flex;
    align-items: center;
    gap: var(--space-1);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-sm);
    font-size: 0.65rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.vehicle-trend.positive {
    background: var(--vehicle-operational-light);
    color: var(--vehicle-operational);
}

.vehicle-trend.negative {
    background: var(--vehicle-critical-light);
    color: var(--vehicle-critical);
}

.vehicle-trend.neutral {
    background: var(--gray-100);
    color: var(--gray-600);
}

.vehicle-last-update {
    font-size: 0.65rem;
    color: var(--gray-500);
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

/* Variantes par type de véhicule */
.vehicle-stat-card.total-vehicles::before {
    background: var(--vehicle-total-gradient);
}

.vehicle-stat-card.total-vehicles .vehicle-card-icon {
    background: var(--vehicle-total-gradient);
}

.vehicle-stat-card.total-vehicles .progress-fill {
    background: var(--vehicle-total-gradient);
}

.vehicle-stat-card.operational-vehicles::before {
    background: var(--vehicle-operational-gradient);
}

.vehicle-stat-card.operational-vehicles .vehicle-card-icon {
    background: var(--vehicle-operational-gradient);
}

.vehicle-stat-card.operational-vehicles .progress-fill {
    background: var(--vehicle-operational-gradient);
}

.vehicle-stat-card.maintenance-vehicles::before {
    background: var(--vehicle-maintenance-gradient);
}

.vehicle-stat-card.maintenance-vehicles .vehicle-card-icon {
    background: var(--vehicle-maintenance-gradient);
}

.vehicle-stat-card.maintenance-vehicles .progress-fill {
    background: var(--vehicle-maintenance-gradient);
}

.vehicle-stat-card.critical-vehicles::before {
    background: var(--vehicle-critical-gradient);
}

.vehicle-stat-card.critical-vehicles .vehicle-card-icon {
    background: var(--vehicle-critical-gradient);
}

.vehicle-stat-card.critical-vehicles .progress-fill {
    background: var(--vehicle-critical-gradient);
}

/* Animations d'entrée */
.vehicle-stat-card {
    opacity: 0;
    transform: translateY(30px);
    animation: slideInUp 0.8s ease-out forwards;
}

.vehicle-stat-card:nth-child(1) { animation-delay: 0.1s; }
.vehicle-stat-card:nth-child(2) { animation-delay: 0.2s; }
.vehicle-stat-card:nth-child(3) { animation-delay: 0.3s; }
.vehicle-stat-card:nth-child(4) { animation-delay: 0.4s; }

@keyframes slideInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive design - Maintenir 4 colonnes sur écrans moyens */
@media (max-width: 1200px) {
    .vehicle-stats-container {
        grid-template-columns: repeat(4, 1fr);
        gap: var(--space-3);
    }

    .vehicle-stat-card {
        min-height: 240px;
        max-height: 240px;
    }

    .vehicle-card-value {
        font-size: 1.8rem;
    }

    .vehicle-card-icon {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }

    .state-detail-text {
        font-size: 0.65rem;
    }

    .state-detail-value {
        font-size: 0.65rem;
    }
}

/* Tablettes - Garder 4 colonnes mais plus compactes */
@media (max-width: 992px) {
    .vehicle-stats-container {
        grid-template-columns: repeat(4, 1fr);
        gap: var(--space-2);
    }

    .vehicle-stat-card {
        min-height: 220px;
        max-height: 220px;
    }

    .vehicle-card-header {
        padding: var(--space-3);
    }

    .vehicle-card-body {
        padding: 0 var(--space-3) var(--space-3);
    }

    .vehicle-card-value {
        font-size: 1.6rem;
    }

    .vehicle-card-title {
        font-size: 0.7rem;
    }

    .vehicle-card-subtitle {
        font-size: 0.6rem;
    }

    .vehicle-card-icon {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }

    .state-detail-item {
        padding: var(--space-1) var(--space-2);
        margin-bottom: var(--space-1);
    }

    .state-detail-text {
        font-size: 0.6rem;
    }

    .state-detail-value {
        font-size: 0.6rem;
        padding: 2px var(--space-1);
    }

    .state-detail-icon {
        width: 16px;
        height: 16px;
        font-size: 0.6rem;
    }

    .progress-label {
        font-size: 0.6rem;
    }

    .vehicle-trend {
        font-size: 0.6rem;
        padding: 2px var(--space-1);
    }

    .vehicle-last-update {
        font-size: 0.6rem;
    }
}

/* Mobiles - Passer à 2 colonnes seulement */
@media (max-width: 768px) {
    .vehicle-stats-container {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--space-3);
        padding: var(--space-4) 0;
    }

    .vehicle-stat-card {
        min-height: 200px;
        max-height: 200px;
    }
}

/* Très petits écrans - 1 colonne */
@media (max-width: 480px) {
    .vehicle-stats-container {
        grid-template-columns: 1fr;
        gap: var(--space-4);
    }

    .vehicle-stat-card {
        min-height: 180px;
        max-height: 180px;
    }

    .vehicle-card-value {
        font-size: 1.8rem;
    }
}
