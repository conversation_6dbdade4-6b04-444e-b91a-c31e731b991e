/* Variables CSS pour les couleurs et styles */
:root {
    /* Couleurs principales */
    --primary-blue: #3b82f6;
    --primary-green: #10b981;
    --primary-orange: #f59e0b;
    --primary-red: #ef4444;
    --primary-purple: #8b5cf6;
    
    /* Gradients magnifiques */
    --gradient-blue: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-green: linear-gradient(135deg, #10b981 0%, #059669 100%);
    --gradient-orange: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    --gradient-red: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    --gradient-purple: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    
    /* Couleurs de fond */
    --bg-blue: rgba(59, 130, 246, 0.1);
    --bg-green: rgba(16, 185, 129, 0.1);
    --bg-orange: rgba(245, 158, 11, 0.1);
    --bg-red: rgba(239, 68, 68, 0.1);
    
    /* Ombres */
    --shadow-light: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-medium: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-heavy: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Espacements */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    
    /* Rayons de bordure */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    
    /* Transitions */
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Container principal des statistiques */
.beautiful-stats-container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--space-6);
    padding: var(--space-8) 0;
    margin-bottom: var(--space-8);
}

/* Carte principale magnifique */
.beautiful-stat-card {
    background: white;
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-medium);
    transition: var(--transition-slow);
    overflow: hidden;
    position: relative;
    min-height: 280px;
    cursor: pointer;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.beautiful-stat-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-heavy);
}

/* Effet de brillance au hover */
.beautiful-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
    z-index: 1;
}

.beautiful-stat-card:hover::before {
    left: 100%;
}

/* En-tête avec gradient de fond */
.beautiful-card-header {
    padding: var(--space-6);
    position: relative;
    background: var(--gradient-blue);
    color: white;
    overflow: hidden;
}

.beautiful-card-header::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(30px, -30px);
}

/* Styles spécifiques par type de carte */
.beautiful-stat-card.total-card .beautiful-card-header {
    background: var(--gradient-blue);
}

.beautiful-stat-card.good-card .beautiful-card-header {
    background: var(--gradient-green);
}

.beautiful-stat-card.fair-card .beautiful-card-header {
    background: var(--gradient-orange);
}

.beautiful-stat-card.bad-card .beautiful-card-header {
    background: var(--gradient-red);
}

/* Informations principales */
.beautiful-card-info {
    position: relative;
    z-index: 2;
}

.beautiful-card-value {
    font-size: 3rem;
    font-weight: 900;
    line-height: 1;
    margin-bottom: var(--space-2);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    background: linear-gradient(45deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0.8));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.beautiful-card-title {
    font-size: 1rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    margin-bottom: var(--space-1);
    opacity: 0.95;
}

.beautiful-card-subtitle {
    font-size: 0.875rem;
    opacity: 0.8;
    font-weight: 500;
}

/* Icône flottante */
.beautiful-card-icon {
    position: absolute;
    top: var(--space-4);
    right: var(--space-4);
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.75rem;
    color: white;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
    transition: var(--transition);
}

.beautiful-stat-card:hover .beautiful-card-icon {
    transform: rotate(360deg) scale(1.1);
    background: rgba(255, 255, 255, 0.3);
}

/* Corps de la carte */
.beautiful-card-body {
    padding: var(--space-6);
    background: linear-gradient(180deg, #fafafa 0%, white 100%);
}

/* Détails avec icônes colorées */
.beautiful-details {
    margin-bottom: var(--space-6);
}

.beautiful-detail-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-3) var(--space-4);
    margin-bottom: var(--space-3);
    background: rgba(255, 255, 255, 0.95); /* Fond blanc plus opaque */
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-light);
    border-left: 4px solid transparent;
    border: 1px solid rgba(0, 0, 0, 0.08); /* Bordure subtile pour définir les contours */
    transition: var(--transition);
}

.beautiful-detail-item:hover {
    transform: translateX(4px);
    box-shadow: var(--shadow-medium);
}

.beautiful-detail-left {
    display: flex !important;
    align-items: center !important;
    gap: var(--space-3) !important;
    flex: 1 !important; /* Prendre l'espace disponible */
}

.beautiful-detail-left .beautiful-detail-text {
    color: #000000 !important; /* Forcer la couleur noire pour le texte seulement */
}

.beautiful-detail-left .beautiful-detail-icon {
    color: white !important; /* Garder les icônes en blanc */
}

.beautiful-detail-left .beautiful-detail-icon i {
    color: white !important; /* Garder les icônes en blanc */
}

.beautiful-detail-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    color: white; /* Couleur blanche pour les icônes dans les cercles colorés */
    box-shadow: var(--shadow-light);
    transition: var(--transition);
}

.beautiful-detail-text {
    font-size: 0.875rem !important;
    font-weight: 700 !important; /* Plus gras pour une meilleure visibilité */
    color: #000000 !important; /* Noir pur pour un contraste maximum */
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important; /* Ombre blanche pour le contraste */
    letter-spacing: 0.025em !important; /* Espacement des lettres pour la lisibilité */
    opacity: 1 !important; /* S'assurer que le texte est visible */
    display: inline !important; /* S'assurer que le texte s'affiche */
}

.beautiful-detail-value {
    font-size: 1rem;
    font-weight: 800;
    color: #1f2937;
    padding: var(--space-2) var(--space-3);
    background: #f3f4f6;
    border-radius: var(--radius-md);
    min-width: 40px;
    text-align: center;
}

/* Barre de progression magnifique */
.beautiful-progress {
    margin-bottom: var(--space-6);
}

.beautiful-progress-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-3);
    font-size: 0.875rem;
    font-weight: 700;
    color: #374151;
}

.beautiful-progress-track {
    height: 12px;
    background: #e5e7eb;
    border-radius: 6px;
    overflow: hidden;
    position: relative;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.beautiful-progress-fill {
    height: 100%;
    border-radius: 6px;
    transition: width 1.5s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.beautiful-progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Pied de carte avec tendances */
.beautiful-card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--space-4);
    border-top: 1px solid #e5e7eb;
}

.beautiful-trend {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.beautiful-trend.positive {
    background: var(--bg-green);
    color: var(--primary-green);
}

.beautiful-trend.negative {
    background: var(--bg-red);
    color: var(--primary-red);
}

.beautiful-trend.neutral {
    background: #f3f4f6;
    color: #6b7280;
}

.beautiful-update {
    font-size: 0.75rem;
    color: #6b7280;
    display: flex;
    align-items: center;
    gap: var(--space-1);
    font-weight: 500;
}

/* Animations d'entrée */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.beautiful-stat-card {
    animation: fadeInUp 0.6s ease-out;
}

.beautiful-stat-card:nth-child(1) { animation-delay: 0.1s; }
.beautiful-stat-card:nth-child(2) { animation-delay: 0.2s; }
.beautiful-stat-card:nth-child(3) { animation-delay: 0.3s; }
.beautiful-stat-card:nth-child(4) { animation-delay: 0.4s; }

/* ========================================
   RESPONSIVE DESIGN COMPLET
   ======================================== */

/* Écrans très larges (1400px+) - Design optimal */
@media (min-width: 1400px) {
    .beautiful-stats-container {
        grid-template-columns: repeat(4, 1fr);
        gap: var(--space-8);
        max-width: 1400px;
        margin: 0 auto;
    }

    .beautiful-stat-card {
        min-height: 320px;
    }

    .beautiful-card-value {
        font-size: 3.5rem;
    }

    .beautiful-card-icon {
        width: 70px;
        height: 70px;
        font-size: 2rem;
    }
}

/* Écrans larges (1200px - 1399px) - Design standard */
@media (min-width: 1200px) and (max-width: 1399px) {
    .beautiful-stats-container {
        grid-template-columns: repeat(4, 1fr);
        gap: var(--space-6);
    }

    .beautiful-stat-card {
        min-height: 300px;
    }

    .beautiful-card-value {
        font-size: 3rem;
    }
}

/* Écrans moyens (992px - 1199px) - 4 colonnes compactes */
@media (min-width: 992px) and (max-width: 1199px) {
    .beautiful-stats-container {
        grid-template-columns: repeat(4, 1fr);
        gap: var(--space-4);
    }

    .beautiful-stat-card {
        min-height: 280px;
    }

    .beautiful-card-header {
        padding: var(--space-4);
    }

    .beautiful-card-body {
        padding: var(--space-4);
    }

    .beautiful-card-value {
        font-size: 2.5rem;
        margin-bottom: var(--space-1);
    }

    .beautiful-card-title {
        font-size: 0.875rem;
    }

    .beautiful-card-subtitle {
        font-size: 0.75rem;
    }

    .beautiful-card-icon {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
    }

    .beautiful-detail-item {
        padding: var(--space-2) var(--space-3);
        margin-bottom: var(--space-2);
    }

    .beautiful-detail-icon {
        width: 28px;
        height: 28px;
        font-size: 0.75rem;
    }

    .beautiful-detail-text {
        font-size: 0.75rem;
        font-weight: 700;
        color: #000000;
        text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
        letter-spacing: 0.025em;
    }

    .beautiful-detail-value {
        font-size: 0.875rem;
        padding: var(--space-1) var(--space-2);
    }
}

/* Tablettes (768px - 991px) - 2 colonnes */
@media (min-width: 768px) and (max-width: 991px) {
    .beautiful-stats-container {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--space-6);
        padding: var(--space-6) 0;
    }

    .beautiful-stat-card {
        min-height: 320px;
    }

    .beautiful-card-header {
        padding: var(--space-6);
    }

    .beautiful-card-body {
        padding: var(--space-6);
    }

    .beautiful-card-value {
        font-size: 3rem;
    }

    .beautiful-card-title {
        font-size: 1rem;
    }

    .beautiful-card-subtitle {
        font-size: 0.875rem;
    }

    .beautiful-card-icon {
        width: 60px;
        height: 60px;
        font-size: 1.75rem;
    }

    .beautiful-details {
        margin-bottom: var(--space-6);
    }

    .beautiful-detail-item {
        padding: var(--space-3) var(--space-4);
        margin-bottom: var(--space-3);
    }

    .beautiful-detail-icon {
        width: 32px;
        height: 32px;
        font-size: 0.875rem;
    }

    .beautiful-detail-text {
        font-size: 0.875rem;
        font-weight: 700;
        color: #000000;
        text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
        letter-spacing: 0.025em;
    }

    .beautiful-detail-value {
        font-size: 1rem;
        padding: var(--space-2) var(--space-3);
    }

    .beautiful-progress {
        margin-bottom: var(--space-6);
    }

    .beautiful-progress-track {
        height: 12px;
    }
}

/* Mobiles larges (576px - 767px) - 1 colonne avec cartes larges */
@media (min-width: 576px) and (max-width: 767px) {
    .beautiful-stats-container {
        grid-template-columns: 1fr;
        gap: var(--space-6);
        padding: var(--space-4) 0;
    }

    .beautiful-stat-card {
        min-height: 280px;
        max-width: 500px;
        margin: 0 auto;
    }

    .beautiful-card-header {
        padding: var(--space-6);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .beautiful-card-body {
        padding: var(--space-6);
    }

    .beautiful-card-value {
        font-size: 2.75rem;
    }

    .beautiful-card-title {
        font-size: 1rem;
        margin-bottom: var(--space-1);
    }

    .beautiful-card-subtitle {
        font-size: 0.875rem;
    }

    .beautiful-card-icon {
        width: 55px;
        height: 55px;
        font-size: 1.6rem;
        position: static;
        margin-left: var(--space-4);
    }

    .beautiful-details {
        display: grid;
        grid-template-columns: 1fr;
        gap: var(--space-3);
        margin-bottom: var(--space-6);
    }

    .beautiful-detail-item {
        padding: var(--space-3) var(--space-4);
        border-radius: var(--radius-lg);
    }

    .beautiful-detail-left {
        gap: var(--space-3);
    }

    .beautiful-detail-icon {
        width: 30px;
        height: 30px;
        font-size: 0.8rem;
    }

    .beautiful-detail-text {
        font-size: 0.875rem;
        font-weight: 700;
        color: #000000;
        text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
        letter-spacing: 0.025em;
    }

    .beautiful-detail-value {
        font-size: 1rem;
        font-weight: 800;
        padding: var(--space-2) var(--space-3);
        min-width: 45px;
    }

    .beautiful-progress-track {
        height: 10px;
    }

    .beautiful-trend {
        padding: var(--space-2) var(--space-4);
        font-size: 0.8rem;
    }

    .beautiful-update {
        font-size: 0.8rem;
    }
}

/* Mobiles (max 575px) - Design ultra-compact */
@media (max-width: 575px) {
    .beautiful-stats-container {
        grid-template-columns: 1fr;
        gap: var(--space-4);
        padding: var(--space-3) var(--space-2);
    }

    .beautiful-stat-card {
        min-height: 240px;
        border-radius: var(--radius-xl);
        margin: 0;
    }

    .beautiful-card-header {
        padding: var(--space-4);
        flex-direction: column;
        text-align: center;
        gap: var(--space-3);
    }

    .beautiful-card-info {
        order: 2;
        text-align: center;
    }

    .beautiful-card-value {
        font-size: 2.5rem;
        margin-bottom: var(--space-2);
    }

    .beautiful-card-title {
        font-size: 0.875rem;
        margin-bottom: var(--space-1);
    }

    .beautiful-card-subtitle {
        font-size: 0.75rem;
    }

    .beautiful-card-icon {
        width: 50px;
        height: 50px;
        font-size: 1.4rem;
        position: static;
        order: 1;
        margin: 0 auto;
    }

    .beautiful-card-body {
        padding: var(--space-4);
    }

    .beautiful-details {
        margin-bottom: var(--space-4);
    }

    .beautiful-detail-item {
        padding: var(--space-2) var(--space-3);
        margin-bottom: var(--space-2);
        border-radius: var(--radius-md);
    }

    .beautiful-detail-left {
        gap: var(--space-2);
    }

    .beautiful-detail-icon {
        width: 24px;
        height: 24px;
        font-size: 0.7rem;
    }

    .beautiful-detail-text {
        font-size: 0.75rem;
        font-weight: 700;
        color: #000000;
        text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
        letter-spacing: 0.025em;
    }

    .beautiful-detail-value {
        font-size: 0.875rem;
        font-weight: 800;
        padding: var(--space-1) var(--space-2);
        min-width: 35px;
    }

    .beautiful-progress {
        margin-bottom: var(--space-4);
    }

    .beautiful-progress-label {
        margin-bottom: var(--space-2);
        font-size: 0.75rem;
    }

    .beautiful-progress-track {
        height: 8px;
        border-radius: 4px;
    }

    .beautiful-progress-fill {
        border-radius: 4px;
    }

    .beautiful-card-footer {
        flex-direction: column;
        gap: var(--space-2);
        align-items: center;
        text-align: center;
        padding-top: var(--space-3);
    }

    .beautiful-trend {
        padding: var(--space-1) var(--space-3);
        font-size: 0.7rem;
        border-radius: var(--radius-sm);
    }

    .beautiful-update {
        font-size: 0.7rem;
    }
}

/* Très petits écrans (max 375px) - Design minimal */
@media (max-width: 375px) {
    .beautiful-stats-container {
        padding: var(--space-2) var(--space-1);
        gap: var(--space-3);
    }

    .beautiful-stat-card {
        min-height: 220px;
        border-radius: var(--radius-lg);
    }

    .beautiful-card-header {
        padding: var(--space-3);
    }

    .beautiful-card-body {
        padding: var(--space-3);
    }

    .beautiful-card-value {
        font-size: 2.2rem;
    }

    .beautiful-card-title {
        font-size: 0.8rem;
    }

    .beautiful-card-subtitle {
        font-size: 0.7rem;
    }

    .beautiful-card-icon {
        width: 45px;
        height: 45px;
        font-size: 1.2rem;
    }

    .beautiful-detail-item {
        padding: var(--space-2);
    }

    .beautiful-detail-text {
        font-size: 0.7rem;
        font-weight: 700;
        color: #000000;
        text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
        letter-spacing: 0.025em;
    }

    .beautiful-detail-value {
        font-size: 0.8rem;
        padding: 2px var(--space-1);
        min-width: 30px;
    }

    .beautiful-detail-icon {
        width: 20px;
        height: 20px;
        font-size: 0.6rem;
    }

    .beautiful-progress-track {
        height: 6px;
    }

    .beautiful-trend {
        font-size: 0.65rem;
        padding: 2px var(--space-2);
    }

    .beautiful-update {
        font-size: 0.65rem;
    }
}

/* ========================================
   OPTIMISATIONS TACTILES MOBILES
   ======================================== */

/* Amélioration des interactions tactiles */
@media (hover: none) and (pointer: coarse) {
    .beautiful-stat-card {
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .beautiful-stat-card:active {
        transform: scale(0.98);
        box-shadow: var(--shadow-sm);
    }

    .beautiful-detail-item:active {
        transform: scale(0.97);
        background: rgba(255, 255, 255, 0.9);
    }

    /* Désactiver les animations complexes sur mobile pour les performances */
    .beautiful-stat-card:hover {
        transform: none;
    }

    .beautiful-card-icon {
        transition: none;
    }

    .beautiful-detail-icon {
        transition: none;
    }

    /* Augmenter les zones de touch sur mobile */
    .beautiful-stat-card {
        min-height: 260px;
        padding: var(--space-1);
    }

    .beautiful-detail-item {
        min-height: 44px; /* Taille minimum recommandée pour le touch */
        padding: var(--space-3);
    }
}

/* Mode sombre adaptatif */
@media (prefers-color-scheme: dark) {
    .beautiful-stat-card {
        background: linear-gradient(135deg,
            rgba(30, 30, 30, 0.95) 0%,
            rgba(50, 50, 50, 0.9) 100%);
        border: 1px solid rgba(255, 255, 255, 0.1);
        color: #ffffff;
    }

    .beautiful-detail-item {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .beautiful-detail-text {
        color: rgba(255, 255, 255, 0.8);
    }

    .beautiful-progress-track {
        background: rgba(255, 255, 255, 0.1);
    }
}

/* Réduction des animations pour les utilisateurs sensibles */
@media (prefers-reduced-motion: reduce) {
    .beautiful-stat-card {
        animation: none;
    }

    .beautiful-progress-fill {
        transition: none;
    }

    .beautiful-card-icon,
    .beautiful-detail-icon {
        transition: none;
    }

    .beautiful-stat-card:hover {
        transform: none;
    }
}

/* Optimisation pour les écrans haute densité */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .beautiful-card-icon {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }

    .beautiful-detail-icon {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* ========================================
   STYLES POUR LES IMAGES DE VÉHICULES
   ======================================== */

/* Images dans la vue tableau */
.vehicle-thumbnail {
    width: 60px;
    height: 45px;
    object-fit: cover;
    border-radius: var(--radius-md);
    border: 2px solid rgba(255, 255, 255, 0.9);
    box-shadow: var(--shadow-light);
    transition: var(--transition);
}

.vehicle-thumbnail:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-medium);
}

.vehicle-img {
    width: 60px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: var(--radius-md);
    overflow: hidden;
}

/* Images dans la vue grille */
.vehicle-card-thumbnail {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: var(--transition);
}

.vehicle-card-img {
    position: relative;
    width: 100%;
    height: 200px;
    overflow: hidden;
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.vehicle-card-img:hover .vehicle-card-thumbnail {
    transform: scale(1.05);
}

/* Icône de remplacement pour les images manquantes */
.vehicle-img i,
.vehicle-card-img i {
    opacity: 0.6;
    transition: var(--transition);
}

.vehicle-img:hover i,
.vehicle-card-img:hover i {
    opacity: 0.8;
    transform: scale(1.1);
}
