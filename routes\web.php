<?php

use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\VehiculeController;
use App\Http\Controllers\DepartementController;
use App\Http\Controllers\EmployeeController;
use App\Http\Controllers\ReceptionController;
use App\Http\Controllers\DocumentController;
use App\Http\Controllers\NomateController;

Route::get('/admin/login', function () {
    return view('admin.admin_login');
});

Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::get('/', function () {
    return view('welcome');
});

// Route de débogage temporaire pour vérifier la configuration PHP
Route::get('/check-php-config', function () {
    phpinfo();
});

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

require __DIR__.'/auth.php';


//AdminGroup Middleware
Route::middleware(['auth', 'roles:admin'])->group(function(){
    Route::get('/admin/dashboard', [AdminController::class, 'AdminDashboard'])->name('admin.dashboard');
    Route::get('/admin/dashboard/logical', [AdminController::class, 'AdminDashboardLogical'])->name('admin.dashboard.logical');
    Route::get('/admin/logout', [AdminController::class, 'AdminLogout'])->name('admin.logout');
    Route::get('/admin/profile', [AdminController::class, 'AdminProfile'])->name('admin.profile');
    Route::post('/admin/profile/store', [AdminController::class, 'AdminProfileStore'])->name('admin.profile.store');
    Route::get('/admin/change/password', [AdminController::class, 'AdminChangePassword'])->name('admin.change.password');
    Route::post('/admin/password/update', [AdminController::class, 'AdminPasswordUpdate'])->name('admin.password.update');
});//End AdminGroup Middleware

Route::get('/admin/login', [AdminController::class, 'AdminLogin'])->name('admin.login');

///Admin Group Middleware
Route::middleware(['auth','roles:admin'])->group(function(){
    //Toutes Les Routes pour les Engins
    Route::controller(VehiculeController::class)->group(function(){
        Route::get('/parc/auto', 'liste_vehicule')->name('pageListeVehicule');
        Route::get('/ajouter/engin', 'AjoutEngin')->name('ajouter.engin');
        Route::get('/editer/engin/{id}', 'EditEngin')->name('editer.engin');
        Route::delete('/supprimer/engin/{id}', 'SuprEngin')->name('supprimer.engin');
        Route::post('/enregistrer/engin', 'EnregEngin')->name('enregistrer.engin');
        Route::put('/modifier/engin', 'ModifEngin')->name('modifier.engin');
    });
    //Toutes Les Routes pour les Engins
    Route::controller(ReceptionController::class)->group(function(){
        Route::get('/reception/liste', 'ListeReception')->name('liste_reception');
        Route::get('/ajouter/reception', 'AjoutReception')->name('ajouter_reception');
        Route::get('/editer/reception/{id}', 'EditReception')->name('editer_reception');
        Route::get('/reception/details/{id}', 'showDetails')->name('reception.details');
        Route::get('/supprimer/reception/{id}', 'SuprReception')->name('supprimer_reception');
        Route::post('/enregistrer/reception', 'EnregReception')->name('enregistrer_reception');
        Route::put('/modifier/reception', 'ModifReception')->name('modifier_reception');
    });
    
    // Routes pour l'affichage des documents
    Route::controller(DocumentController::class)->group(function(){
        Route::get('/documents/pv/{filename}', 'showPv')->name('documents.show_pv');
    });

    //Redirection de la route racine vers /departements
    Route::redirect('/', '/departements');
    
    //Toutes Les Routes pour les departements
    Route::controller(DepartementController::class)->group(function(){
        Route::get('/departements', 'index')->name('departements.index');
        Route::get('/departements/edit/{id}', 'edit')->name('departement.edit');
        Route::put('/departements/update/{id}', 'update')->name('departements.update');
        Route::get('/departements/create', 'create')->name('departements.create');
        Route::post('/departements/store', 'store')->name('departements.store');
        Route::delete('/departements/delete/{id}', 'delete')->name('departements.delete');
        Route::get('/departements/deleted', 'deleted')->name('departements.deleted');
        Route::put('/departements/restore/{id}', 'restore')->name('departements.restore');
    });
    
    //Toutes Les Routes pour les employés
    Route::controller(EmployeeController::class)->group(function(){
        Route::get('/employees', 'index')->name('employees.index');
        Route::get('/employees/create', 'create')->name('employees.create');
        Route::post('/employees/store', 'store')->name('employees.store');
        Route::get('/employees/details/{id}', 'details')->name('employees.details');
        Route::get('/employees/edit/{id}', 'edit')->name('employees.edit');
        Route::put('/employees/update/{id}', 'update')->name('employees.update');
        Route::delete('/employees/destroy/{id}', 'destroy')->name('employees.destroy');
        Route::get('/employees/deleted', 'deleted')->name('employees.deleted');
        Route::put('/employees/restore/{id}', 'restore')->name('employees.restore');
    });
    
    //Toutes Les Routes pour Nomate (Nomenclature Comptable)
    Route::controller(NomateController::class)->group(function(){
        Route::get('/nomate', 'index')->name('nomate.index');
        Route::get('/nomate/search', 'search')->name('nomate.search');
        Route::get('/nomate/{id}', 'show')->name('nomate.show');
        Route::get('/nomate/{id}/edit', 'edit')->name('nomate.edit');
        Route::put('/nomate/{id}', 'update')->name('nomate.update');
    });
    
    
    // Routes pour le module Comptabilité Matière
    Route::prefix('comptabilite-matiere')->group(function() {
        // Routes pour les entrées
        Route::middleware('comptabilite.matiere:entrees.manage')->group(function() {
            Route::get('/entrees', [App\Http\Controllers\ComptabiliteMatiere\EntreeController::class, 'index'])->name('entrees.index');
            Route::get('/entrees/create', [App\Http\Controllers\ComptabiliteMatiere\EntreeController::class, 'create'])->name('entrees.create');
            Route::post('/entrees', [App\Http\Controllers\ComptabiliteMatiere\EntreeController::class, 'store'])->name('entrees.store');
            Route::get('/entrees/{id}', [App\Http\Controllers\ComptabiliteMatiere\EntreeController::class, 'show'])->name('entrees.show');
            Route::get('/entrees/{id}/edit', [App\Http\Controllers\ComptabiliteMatiere\EntreeController::class, 'edit'])->name('entrees.edit');
            Route::put('/entrees/{id}', [App\Http\Controllers\ComptabiliteMatiere\EntreeController::class, 'update'])->name('entrees.update');
            Route::delete('/entrees/{id}', [App\Http\Controllers\ComptabiliteMatiere\EntreeController::class, 'destroy'])->name('entrees.destroy');
        });
        
        // Routes pour les sorties
        Route::middleware('comptabilite.matiere:sorties.manage')->group(function() {
            Route::get('/sorties', [App\Http\Controllers\ComptabiliteMatiere\SortieController::class, 'index'])->name('sorties.index');
            Route::get('/sorties/create', [App\Http\Controllers\ComptabiliteMatiere\SortieController::class, 'create'])->name('sorties.create');
            Route::post('/sorties', [App\Http\Controllers\ComptabiliteMatiere\SortieController::class, 'store'])->name('sorties.store');
            Route::get('/sorties/{id}', [App\Http\Controllers\ComptabiliteMatiere\SortieController::class, 'show'])->name('sorties.show');
            Route::get('/sorties/{id}/edit', [App\Http\Controllers\ComptabiliteMatiere\SortieController::class, 'edit'])->name('sorties.edit');
            Route::put('/sorties/{id}', [App\Http\Controllers\ComptabiliteMatiere\SortieController::class, 'update'])->name('sorties.update');
            Route::delete('/sorties/{id}', [App\Http\Controllers\ComptabiliteMatiere\SortieController::class, 'destroy'])->name('sorties.destroy');
        });
        
        // Routes pour les ajustements
        Route::middleware('comptabilite.matiere:ajustements.manage')->group(function() {
            Route::get('/ajustements', [App\Http\Controllers\ComptabiliteMatiere\AjustementController::class, 'index'])->name('ajustements.index');
            Route::get('/ajustements/create', [App\Http\Controllers\ComptabiliteMatiere\AjustementController::class, 'create'])->name('ajustements.create');
            Route::post('/ajustements', [App\Http\Controllers\ComptabiliteMatiere\AjustementController::class, 'store'])->name('ajustements.store');
            Route::get('/ajustements/{id}', [App\Http\Controllers\ComptabiliteMatiere\AjustementController::class, 'show'])->name('ajustements.show');
            Route::get('/ajustements/{id}/edit', [App\Http\Controllers\ComptabiliteMatiere\AjustementController::class, 'edit'])->name('ajustements.edit');
            Route::put('/ajustements/{id}', [App\Http\Controllers\ComptabiliteMatiere\AjustementController::class, 'update'])->name('ajustements.update');
            Route::delete('/ajustements/{id}', [App\Http\Controllers\ComptabiliteMatiere\AjustementController::class, 'destroy'])->name('ajustements.destroy');
        });
        
        // Routes pour les immobilisations
        Route::middleware('comptabilite.matiere:immobilisations.manage')->group(function() {
            Route::get('/immobilisations', [App\Http\Controllers\ComptabiliteMatiere\ImmobilisationController::class, 'index'])->name('immobilisations.index');
            Route::get('/immobilisations/create', [App\Http\Controllers\ComptabiliteMatiere\ImmobilisationController::class, 'create'])->name('immobilisations.create');
            Route::post('/immobilisations', [App\Http\Controllers\ComptabiliteMatiere\ImmobilisationController::class, 'store'])->name('immobilisations.store');
            Route::get('/immobilisations/{id}', [App\Http\Controllers\ComptabiliteMatiere\ImmobilisationController::class, 'show'])->name('immobilisations.show');
            Route::get('/immobilisations/{id}/edit', [App\Http\Controllers\ComptabiliteMatiere\ImmobilisationController::class, 'edit'])->name('immobilisations.edit');
            Route::put('/immobilisations/{id}', [App\Http\Controllers\ComptabiliteMatiere\ImmobilisationController::class, 'update'])->name('immobilisations.update');
            Route::delete('/immobilisations/{id}', [App\Http\Controllers\ComptabiliteMatiere\ImmobilisationController::class, 'destroy'])->name('immobilisations.destroy');
        });
        
        // Routes pour les réformes
        Route::middleware('comptabilite.matiere:reformes.manage')->group(function() {
            Route::get('/reformes', [App\Http\Controllers\ComptabiliteMatiere\ReformeController::class, 'index'])->name('reformes.index');
            Route::get('/reformes/create', [App\Http\Controllers\ComptabiliteMatiere\ReformeController::class, 'create'])->name('reformes.create');
            Route::post('/reformes', [App\Http\Controllers\ComptabiliteMatiere\ReformeController::class, 'store'])->name('reformes.store');
            Route::get('/reformes/{id}', [App\Http\Controllers\ComptabiliteMatiere\ReformeController::class, 'show'])->name('reformes.show');
            Route::get('/reformes/{id}/edit', [App\Http\Controllers\ComptabiliteMatiere\ReformeController::class, 'edit'])->name('reformes.edit');
            Route::put('/reformes/{id}', [App\Http\Controllers\ComptabiliteMatiere\ReformeController::class, 'update'])->name('reformes.update');
            Route::delete('/reformes/{id}', [App\Http\Controllers\ComptabiliteMatiere\ReformeController::class, 'destroy'])->name('reformes.destroy');
        });
        
        // Routes pour les mutations
        Route::middleware('comptabilite.matiere:mutations.manage')->group(function() {
            Route::get('/mutations', [App\Http\Controllers\ComptabiliteMatiere\MutationController::class, 'index'])->name('mutations.index');
            Route::get('/mutations/create', [App\Http\Controllers\ComptabiliteMatiere\MutationController::class, 'create'])->name('mutations.create');
            Route::post('/mutations', [App\Http\Controllers\ComptabiliteMatiere\MutationController::class, 'store'])->name('mutations.store');
            Route::get('/mutations/{id}', [App\Http\Controllers\ComptabiliteMatiere\MutationController::class, 'show'])->name('mutations.show');
            Route::get('/mutations/{id}/edit', [App\Http\Controllers\ComptabiliteMatiere\MutationController::class, 'edit'])->name('mutations.edit');
            Route::put('/mutations/{id}', [App\Http\Controllers\ComptabiliteMatiere\MutationController::class, 'update'])->name('mutations.update');
            Route::delete('/mutations/{id}', [App\Http\Controllers\ComptabiliteMatiere\MutationController::class, 'destroy'])->name('mutations.destroy');
        });
        
        // Routes pour les inventaires
        Route::middleware('comptabilite.matiere:inventaires.manage')->group(function() {
            Route::get('/inventaires', [App\Http\Controllers\ComptabiliteMatiere\InventaireController::class, 'index'])->name('inventaires.index');
            Route::get('/inventaires/create', [App\Http\Controllers\ComptabiliteMatiere\InventaireController::class, 'create'])->name('inventaires.create');
            Route::post('/inventaires', [App\Http\Controllers\ComptabiliteMatiere\InventaireController::class, 'store'])->name('inventaires.store');
            Route::get('/inventaires/{id}', [App\Http\Controllers\ComptabiliteMatiere\InventaireController::class, 'show'])->name('inventaires.show');
            Route::get('/inventaires/{id}/edit', [App\Http\Controllers\ComptabiliteMatiere\InventaireController::class, 'edit'])->name('inventaires.edit');
            Route::put('/inventaires/{id}', [App\Http\Controllers\ComptabiliteMatiere\InventaireController::class, 'update'])->name('inventaires.update');
            Route::delete('/inventaires/{id}', [App\Http\Controllers\ComptabiliteMatiere\InventaireController::class, 'destroy'])->name('inventaires.destroy');
            Route::post('/inventaires/{id}/cloturer', [App\Http\Controllers\ComptabiliteMatiere\InventaireController::class, 'cloturer'])->name('inventaires.cloturer');
        });
        
        // Routes pour les rapports avec middleware de sécurité
        Route::middleware('comptabilite.matiere:rapports.view')->group(function() {
            // Page d'index des rapports
            Route::get('/rapports', [App\Http\Controllers\ComptabiliteMatiere\RapportController::class, 'index'])->name('rapports.index');
            
            // Routes pour la fiche de stock
            Route::get('/rapports/fiche-stock-form', [App\Http\Controllers\ComptabiliteMatiere\RapportController::class, 'ficheStockForm'])->name('rapports.fiche-stock-form');
            Route::post('/rapports/fiche-stock', [App\Http\Controllers\ComptabiliteMatiere\RapportController::class, 'ficheStock'])->name('rapports.fiche-stock');
            Route::get('/rapports/fiche-stock', [App\Http\Controllers\ComptabiliteMatiere\RapportController::class, 'ficheStock'])->name('rapports.fiche-stock.get');
            
            // Routes pour le journal matière
            Route::get('/rapports/journal-matiere-form', [App\Http\Controllers\ComptabiliteMatiere\RapportController::class, 'journalMatiereForm'])->name('rapports.journal-matiere-form');
            Route::post('/rapports/journal-matiere', [App\Http\Controllers\ComptabiliteMatiere\RapportController::class, 'journalMatiere'])->name('rapports.journal-matiere');
            Route::get('/rapports/journal-matiere', [App\Http\Controllers\ComptabiliteMatiere\RapportController::class, 'journalMatiere'])->name('rapports.journal-matiere.get');
            
            // Routes pour le grand livre
            Route::get('/rapports/grand-livre-form', [App\Http\Controllers\ComptabiliteMatiere\RapportController::class, 'grandLivreForm'])->name('rapports.grand-livre-form');
            Route::post('/rapports/grand-livre', [App\Http\Controllers\ComptabiliteMatiere\RapportController::class, 'grandLivre'])->name('rapports.grand-livre');
            Route::get('/rapports/grand-livre', [App\Http\Controllers\ComptabiliteMatiere\RapportController::class, 'grandLivre'])->name('rapports.grand-livre.get');
            
            // Routes pour le rapport d'inventaire
            Route::get('/rapports/inventaire-form', [App\Http\Controllers\ComptabiliteMatiere\RapportController::class, 'rapportInventaireForm'])->name('rapports.inventaire-form');
            Route::get('/rapports/inventaire/{id}', [App\Http\Controllers\ComptabiliteMatiere\RapportController::class, 'rapportInventaire'])->name('rapports.inventaire');
        });
        
        // Routes pour les exports PDF avec middleware spécifique pour l'export
        Route::middleware('comptabilite.matiere:rapports.export')->group(function() {
            // Routes pour le rapport des immobilisations (avec export PDF)
            Route::get('/rapports/immobilisations-form', [App\Http\Controllers\ComptabiliteMatiere\RapportController::class, 'rapportImmobilisationsForm'])->name('rapports.immobilisations-form');
            Route::post('/rapports/immobilisations', [App\Http\Controllers\ComptabiliteMatiere\RapportController::class, 'rapportImmobilisations'])->name('rapports.immobilisations');
            Route::get('/rapports/immobilisations', [App\Http\Controllers\ComptabiliteMatiere\RapportController::class, 'rapportImmobilisations'])->name('rapports.immobilisations.get');
        });
    });

});//End Group Admin Middleware

// Route dédiée pour les détails des employés (complètement en dehors du middleware)
Route::get('/api/employee/{id}', [EmployeeController::class, 'getEmployeeDetails']);

// Routes de débogage pour la base de données
Route::get('/api/test-db-connection', function() {
    try {
        $connection = DB::connection()->getPdo();
        $databaseName = DB::connection()->getDatabaseName();
        
        return response()->json([
            'success' => true,
            'message' => 'Connexion à la base de données établie avec succès',
            'database' => $databaseName,
            'driver' => DB::connection()->getDriverName(),
            'version' => $connection->getAttribute(PDO::ATTR_SERVER_VERSION)
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Erreur de connexion à la base de données',
            'error' => $e->getMessage()
        ], 500);
    }
});

Route::get('/api/inspect-employee-table', function() {
    try {
        $columns = DB::getSchemaBuilder()->getColumnListing('employee');
        $sample = DB::table('employee')->first();
        
        return response()->json([
            'success' => true,
            'table' => 'employee',
            'columns' => $columns,
            'sample_record' => $sample,
            'column_types' => array_map(function($column) {
                return [
                    'name' => $column,
                    'type' => DB::connection()->getDoctrineColumn('employee', $column)->getType()->getName()
                ];
            }, $columns)
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Erreur lors de l\'inspection de la table employee',
            'error' => $e->getMessage()
        ], 500);
    }
});

