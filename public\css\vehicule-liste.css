/* Styles généraux */
:root {
    --primary-color: #4361ee;
    --secondary-color: #3f37c9;
    --accent-color: #f72585;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-400: #ced4da;
    --gray-500: #adb5bd;
    --gray-600: #6c757d;
    --gray-700: #495057;
    --gray-800: #343a40;
    --gray-900: #212529;
    --border-radius: 0.5rem;
    --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --transition: all 0.3s ease;
}

/* Header et navigation */
.page-header {
    background: linear-gradient(120deg, #4361ee 0%, #7209b7 100%);
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--box-shadow);
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB2aWV3Qm94PSIwIDAgMTI4MCAxNDAiIHByZXNlcnZlQXNwZWN0UmF0aW89Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0icmdiYSgyNTUsMjU1LDI1NSwwLjEpIj48cGF0aCBkPSJNMTI4MCAwTDY0MCAxNDBMMCAweiIvPjwvZz48L3N2Zz4=');
    background-size: 100% 100%;
    opacity: 0.2;
    z-index: 0;
}

.page-header h4 {
    color: white;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
    position: relative;
    z-index: 1;
}

.page-header p {
    color: rgba(255,255,255,0.9);
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    max-width: 700px;
    position: relative;
    z-index: 1;
}

.page-header .breadcrumb {
    background: transparent;
    padding: 0;
    margin: 0;
    position: relative;
    z-index: 1;
}

.page-header .breadcrumb-item, 
.page-header .breadcrumb-item a {
    color: rgba(255,255,255,0.8);
    font-weight: 500;
    text-decoration: none;
}

.page-header .breadcrumb-item.active {
    color: rgba(255,255,255,1);
}

.page-header .breadcrumb-item+.breadcrumb-item::before {
    color: rgba(255,255,255,0.6);
}

.btn-add-vehicle {
    background: linear-gradient(45deg, #f72585, #b5179e);
    border: none;
    border-radius: 50px;
    padding: 0.75rem 1.5rem;
    color: white;
    font-weight: 600;
    box-shadow: 0 4px 10px rgba(0,0,0,0.15);
    transition: var(--transition);
    position: relative;
    z-index: 1;
    overflow: hidden;
}

.btn-add-vehicle:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(0,0,0,0.2);
    color: white;
}

.btn-add-vehicle::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, #b5179e, #f72585);
    z-index: -1;
    transition: opacity 0.3s ease;
    opacity: 0;
}

.btn-add-vehicle:hover::after {
    opacity: 1;
}

.btn-add-vehicle i {
    margin-right: 0.5rem;
    font-size: 1.1rem;
}

/* Cartes statistiques modernes - Design dashboard appliqué */
.dashboard-stats-container {
    padding: 2rem 0;
}

.stat-card {
    background: #ffffff;
    border-radius: 24px;
    border: 1px solid rgba(0,0,0,0.05);
    box-shadow: 0 10px 40px rgba(0,0,0,0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    position: relative;
    height: 280px;
    backdrop-filter: blur(10px);
}

/* Bordure latérale colorée */
.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 24px 0 0 24px;
    opacity: 1;
    transition: all 0.4s ease;
}

.stat-card:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow: 0 20px 60px rgba(0,0,0,0.15);
}

.stat-card:hover::before {
    width: 12px;
    box-shadow: 0 0 20px rgba(0,0,0,0.2);
}

.stat-card-header {
    padding: 1.5rem 1.5rem 0 2rem;
    position: relative;
    z-index: 2;
}

.stat-card-icon {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.2rem;
    color: white;
    box-shadow: 0 8px 32px rgba(0,0,0,0.2);
    backdrop-filter: blur(10px);
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-8px); }
}

.stat-card-header.good .stat-card-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4);
}

.stat-card-header.warning .stat-card-icon {
    background: linear-gradient(135deg, #ffa726 0%, #ff7043 100%);
    box-shadow: 0 8px 32px rgba(255, 167, 38, 0.4);
}

.stat-card-header.bad .stat-card-icon {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    box-shadow: 0 8px 32px rgba(255, 107, 107, 0.4);
}

.stat-card-body {
    padding: 2rem 2rem 1rem 2rem;
    height: calc(100% - 80px);
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 2;
}

.stat-card-value {
    font-size: 3rem;
    font-weight: 800;
    color: #1e293b;
    line-height: 1;
    margin-bottom: 0.5rem;
    animation: countUp 2s ease-out;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

@keyframes countUp {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.stat-card-subtitle {
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: #475569;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.stat-card-footer {
    margin-top: auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
}

.stat-card-footer span {
    font-size: 0.85rem;
    color: #64748b;
    font-weight: 500;
}

.stat-card-trend {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.6rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    border: none;
    position: relative;
    overflow: hidden;
}

.stat-card-trend.up {
    background: rgba(16, 185, 129, 0.1);
    color: #047857;
    border: 2px solid rgba(16, 185, 129, 0.2);
}

.stat-card-trend.up::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.05), rgba(5, 150, 105, 0.05));
    z-index: -1;
}

.stat-card-trend.down {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border: 2px solid rgba(239, 68, 68, 0.2);
}

.stat-card-trend.down::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.05), rgba(220, 38, 38, 0.05));
    z-index: -1;
}

/* Bordures colorées par type */
.stat-card:has(.stat-card-header.good) {
    border-left: 6px solid #667eea;
    border-right: 4px solid rgba(102, 126, 234, 0.3);
    border-top: 2px solid rgba(102, 126, 234, 0.2);
    border-bottom: 2px solid rgba(102, 126, 234, 0.2);
}

.stat-card:has(.stat-card-header.good)::before {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.4);
}

.stat-card:has(.stat-card-header.warning) {
    border-left: 6px solid #ffa726;
    border-right: 4px solid rgba(255, 167, 38, 0.3);
    border-top: 2px solid rgba(255, 167, 38, 0.2);
    border-bottom: 2px solid rgba(255, 167, 38, 0.2);
}

.stat-card:has(.stat-card-header.warning)::before {
    background: linear-gradient(135deg, #ffa726 0%, #ff7043 100%);
    box-shadow: 0 0 20px rgba(255, 167, 38, 0.4);
}

.stat-card:has(.stat-card-header.bad) {
    border-left: 6px solid #ff6b6b;
    border-right: 4px solid rgba(255, 107, 107, 0.3);
    border-top: 2px solid rgba(255, 107, 107, 0.2);
    border-bottom: 2px solid rgba(255, 107, 107, 0.2);
}

.stat-card:has(.stat-card-header.bad)::before {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    box-shadow: 0 0 20px rgba(255, 107, 107, 0.4);
}

/* Effets de survol spécialisés */
.stat-card:has(.stat-card-header.good):hover {
    box-shadow: 0 20px 60px rgba(102, 126, 234, 0.2), 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.stat-card:has(.stat-card-header.warning):hover {
    box-shadow: 0 20px 60px rgba(255, 167, 38, 0.2), 0 0 0 2px rgba(255, 167, 38, 0.1);
}

.stat-card:has(.stat-card-header.bad):hover {
    box-shadow: 0 20px 60px rgba(255, 107, 107, 0.2), 0 0 0 2px rgba(255, 107, 107, 0.1);
}

/* Animations d'entrée */
.animate-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 0.8s ease-out forwards;
}

.delay-1 { animation-delay: 0.1s; }
.delay-2 { animation-delay: 0.2s; }
.delay-3 { animation-delay: 0.3s; }
.delay-4 { animation-delay: 0.4s; }

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Filtres et recherche */
.filters-card {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.1);
    margin-bottom: 1.5rem;
    background: white;
}

.filters-card-body {
    padding: 1.5rem;
}

.search-box {
    position: relative;
    margin-bottom: 1rem;
}

.search-box input {
    padding-left: 3rem;
    border-radius: 50px;
    height: 3rem;
    border: 1px solid var(--gray-300);
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    transition: var(--transition);
}

.search-box input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.25);
}

.search-box i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-500);
    font-size: 1.25rem;
}

.filter-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.filter-btn {
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-weight: 500;
    transition: var(--transition);
    display: flex;
    align-items: center;
}

.filter-btn i {
    margin-right: 0.5rem;
    font-size: 1rem;
}

.filter-btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.advanced-filters {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--gray-200);
}

.advanced-filters-toggle {
    cursor: pointer;
    color: var(--primary-color);
    font-weight: 500;
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.advanced-filters-toggle i {
    margin-right: 0.5rem;
    transition: var(--transition);
}

.advanced-filters-toggle.collapsed i {
    transform: rotate(-90deg);
}

/* Tableau des véhicules */
.vehicle-table-card {
    border-radius: 20px;
    border: none;
    box-shadow: 0 10px 40px rgba(0,0,0,0.15);
    overflow: hidden;
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    backdrop-filter: blur(10px);
}

.table-responsive {
    overflow-x: auto;
}

.table {
    margin-bottom: 0;
    background: transparent;
    border-radius: 20px;
    overflow: hidden;
}

.table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-weight: 700;
    border: none;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 1px;
    padding: 1.5rem 1rem;
    vertical-align: middle;
    position: sticky;
    top: 0;
    z-index: 10;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.table tbody td {
    padding: 1.5rem 1rem;
    vertical-align: middle;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    color: var(--gray-800);
    font-size: 0.9rem;
    background: rgba(255,255,255,0.8);
    transition: all 0.3s ease;
}

.table tbody tr:last-child td {
    border-bottom: none;
}

.table tbody tr {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.table tbody tr:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    transform: scale(1.01);
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.vehicle-img {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transition: var(--transition);
}

.vehicle-img:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 16px rgba(0,0,0,0.15);
}

.vehicle-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.vehicle-status {
    padding: 0.35rem 0.75rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-flex;
    align-items: center;
}

.vehicle-status i {
    margin-right: 0.35rem;
    font-size: 0.875rem;
}

.vehicle-status.good {
    background-color: rgba(76, 175, 80, 0.1);
    color: #2e7d32;
}

.vehicle-status.fair {
    background-color: rgba(255, 152, 0, 0.1);
    color: #e65100;
}

.vehicle-status.bad {
    background-color: rgba(244, 67, 54, 0.1);
    color: #b71c1c;
}

.btn-action {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    font-size: 1rem;
    margin: 0 0.25rem;
}

.btn-action:hover {
    transform: translateY(-3px);
}

.btn-action.view {
    background-color: rgba(67, 97, 238, 0.1);
    color: var(--primary-color);
}

.btn-action.view:hover {
    background-color: var(--primary-color);
    color: white;
}

.btn-action.edit {
    background-color: rgba(255, 152, 0, 0.1);
    color: #e65100;
}

.btn-action.edit:hover {
    background-color: #e65100;
    color: white;
}

.btn-action.delete {
    background-color: rgba(244, 67, 54, 0.1);
    color: #b71c1c;
}

.btn-action.delete:hover {
    background-color: #b71c1c;
    color: white;
}

/* Modal de détails */
.vehicle-modal .modal-content {
    border: none;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.vehicle-modal .modal-header {
    background: linear-gradient(120deg, #4361ee 0%, #3a0ca3 100%);
    color: white;
    border-bottom: none;
    padding: 1.5rem;
}

.vehicle-modal .modal-title {
    font-weight: 600;
    display: flex;
    align-items: center;
}

.vehicle-modal .modal-title i {
    margin-right: 0.5rem;
    font-size: 1.25rem;
}

.vehicle-modal .btn-close {
    color: white;
    opacity: 0.8;
}

.vehicle-modal .modal-body {
    padding: 1.5rem;
}

.vehicle-img-lg {
    height: 250px;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.vehicle-img-lg img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.info-card {
    background-color: var(--gray-100);
    border-radius: var(--border-radius);
    padding: 1.25rem;
    height: 100%;
}

.info-card h6 {
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--gray-800);
    border-bottom: 2px solid var(--gray-300);
    padding-bottom: 0.5rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.75rem;
}

.info-label {
    font-weight: 500;
    color: var(--gray-600);
}

.info-value {
    font-weight: 600;
    color: var(--gray-800);
}

.vehicle-modal .modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--gray-200);
}

/* Pagination */
.pagination {
    margin-top: 1.5rem;
    justify-content: center;
}

.pagination .page-item .page-link {
    border: none;
    margin: 0 0.25rem;
    border-radius: 50%;
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-700);
    font-weight: 500;
    transition: var(--transition);
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    color: white;
}

.pagination .page-item .page-link:hover {
    background-color: var(--gray-200);
    color: var(--gray-800);
}

.pagination .page-item.active .page-link:hover {
    background-color: var(--primary-color);
    color: white;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease forwards;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(67, 97, 238, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(67, 97, 238, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(67, 97, 238, 0);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

/* Responsive */
@media (max-width: 992px) {
    .page-header {
        padding: 1.5rem;
    }
    
    .page-header h4 {
        font-size: 1.5rem;
    }
    
    .stat-card {
        height: 240px;
    }

    .stat-card-icon {
        width: 60px;
        height: 60px;
        font-size: 2rem;
        top: 1.2rem;
        right: 1.2rem;
    }

    .stat-card-value {
        font-size: 2.5rem;
    }

    .stat-card-body {
        padding: 1.5rem;
    }

    .stat-card-subtitle {
        font-size: 0.8rem;
        margin-bottom: 0.8rem;
    }
}

@media (max-width: 768px) {
    .filter-buttons {
        flex-wrap: nowrap;
        overflow-x: auto;
        padding-bottom: 0.5rem;
    }
    
    .filter-btn {
        white-space: nowrap;
    }
    
    .table thead th {
        font-size: 0.7rem;
        padding: 0.75rem 0.5rem;
    }
    
    .table tbody td {
        padding: 0.75rem 0.5rem;
    }
    
    .btn-action {
        width: 1.75rem;
        height: 1.75rem;
        font-size: 0.875rem;
    }
}

@media (max-width: 576px) {
    .page-header {
        padding: 1.25rem;
    }
    
    .page-header h4 {
        font-size: 1.25rem;
    }
    
    .btn-add-vehicle {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }
    
    .stat-card {
        height: 220px;
    }

    .stat-card-icon {
        width: 50px;
        height: 50px;
        font-size: 1.8rem;
        top: 1rem;
        right: 1rem;
    }

    .stat-card-value {
        font-size: 2rem;
    }

    .stat-card-body {
        padding: 1.2rem;
    }

    .stat-card-subtitle {
        font-size: 0.75rem;
        margin-bottom: 0.6rem;
    }
    
    .vehicle-img {
        width: 50px;
        height: 50px;
    }
}

/* Vue en grille améliorée */
/* ===== NOUVELLE INTERFACE GRILLE MODERNE ===== */
#gridView {
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    gap: 2rem;
    padding: 2rem 0;
    align-items: start;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 20px;
    margin: 1rem 0;
    position: relative;
}

/* Pattern de fond subtil */
#gridView::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(99, 102, 241, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.05) 0%, transparent 50%);
    border-radius: 20px;
    pointer-events: none;
}

/* Affichage de la grille quand elle n'a pas la classe d-none */
#gridView:not(.d-none) {
    display: grid !important;
}

/* Cacher la grille quand elle a la classe d-none */
#gridView.d-none {
    display: none !important;
}

/* Vue tableau */
#tableView {
    display: block;
}

#tableView.d-none {
    display: none !important;
}

#tableView:not(.d-none) {
    display: block !important;
}

/* Grille responsive pour différentes tailles d'écran */
@media (min-width: 1600px) {
    #gridView {
        grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));
        gap: 2.5rem;
        padding: 3rem;
    }
}

@media (min-width: 1200px) and (max-width: 1599px) {
    #gridView {
        grid-template-columns: repeat(auto-fill, minmax(340px, 1fr));
        gap: 2rem;
        padding: 2.5rem;
    }
}

@media (max-width: 768px) {
    #gridView {
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1.5rem;
        padding: 1.5rem;
        margin: 0.5rem 0;
    }
}

@media (max-width: 576px) {
    #gridView {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 1rem;
    }

    .grid-sort-options {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }

    .grid-sort-options .form-select {
        width: 100%;
    }

    .d-flex.justify-content-between.align-items-center {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .d-flex.align-items-center.gap-3 {
        flex-direction: column;
        gap: 1rem !important;
    }

    .view-switch {
        justify-content: center;
    }
}

/* ===== CARTES VÉHICULES MODERNES ===== */
.vehicle-card {
    border-radius: 28px;
    border: none;
    box-shadow:
        0 20px 60px rgba(0,0,0,0.08),
        0 8px 25px rgba(0,0,0,0.04),
        0 2px 8px rgba(0,0,0,0.02);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    position: relative;
    height: fit-content;
    min-height: 580px;
    display: flex;
    flex-direction: column;
    backdrop-filter: blur(20px);
    border: 2px solid rgba(255,255,255,0.9);
    margin-bottom: 0;
    z-index: 1;
}

/* Effet de brillance subtil */
.vehicle-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255,255,255,0.4),
        transparent
    );
    transition: left 0.8s ease;
    z-index: 2;
    pointer-events: none;
}

.vehicle-card:hover::after {
    left: 100%;
}

.vehicle-card:hover {
    transform: translateY(-20px) scale(1.04);
    box-shadow:
        0 35px 100px rgba(67, 97, 238, 0.25),
        0 15px 40px rgba(0,0,0,0.12),
        0 5px 15px rgba(0,0,0,0.08);
    border-color: rgba(102, 126, 234, 0.4);
    z-index: 10;
}

/* Bordure supérieure colorée */
.vehicle-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 30%, #f093fb 70%, #f5576c 100%);
    opacity: 0;
    transition: all 0.5s ease;
    border-radius: 28px 28px 0 0;
    z-index: 3;
}

.vehicle-card:hover::before {
    opacity: 1;
    height: 8px;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
}

/* ===== SECTION IMAGE AMÉLIORÉE ===== */
.vehicle-card-img {
    height: 280px;
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 30%, #f093fb 70%, #f5576c 100%);
    border-radius: 28px 28px 0 0;
    box-shadow:
        inset 0 -6px 12px rgba(0,0,0,0.15),
        inset 0 2px 4px rgba(255,255,255,0.1);
    z-index: 1;
}

/* Overlay gradient pour améliorer la lisibilité */
.vehicle-card-img::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        180deg,
        rgba(0,0,0,0.1) 0%,
        transparent 30%,
        transparent 70%,
        rgba(0,0,0,0.2) 100%
    );
    z-index: 2;
    transition: opacity 0.4s ease;
}

.vehicle-card:hover .vehicle-card-img::before {
    opacity: 0.7;
}

.vehicle-card-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    filter: brightness(1.05) contrast(1.1) saturate(1.1);
}

.vehicle-card:hover .vehicle-card-img img {
    transform: scale(1.08) rotate(1deg);
    filter: brightness(1.1) contrast(1.15) saturate(1.2);
}

.vehicle-card-thumbnail {
    border-radius: 0;
    position: relative;
    z-index: 1;
}

/* ===== BADGES DE STATUT MODERNES ===== */
.vehicle-card-status {
    position: absolute;
    top: 1.25rem;
    right: 1.25rem;
    padding: 0.75rem 1.25rem;
    border-radius: 50px;
    font-size: 0.8rem;
    font-weight: 800;
    text-transform: uppercase;
    letter-spacing: 1.2px;
    box-shadow:
        0 8px 32px rgba(0,0,0,0.2),
        0 4px 16px rgba(0,0,0,0.1);
    backdrop-filter: blur(20px);
    border: 3px solid rgba(255,255,255,0.4);
    z-index: 5;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

/* Effet de brillance sur les badges */
.vehicle-card-status::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.6s ease;
}

.vehicle-card:hover .vehicle-card-status::before {
    left: 100%;
}

.vehicle-card-status.good {
    background: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
    color: white;
    box-shadow:
        0 8px 32px rgba(16, 185, 129, 0.4),
        0 4px 16px rgba(16, 185, 129, 0.2);
    border-color: rgba(255,255,255,0.5);
}

.vehicle-card-status.fair {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 50%, #b45309 100%);
    color: white;
    box-shadow:
        0 8px 32px rgba(245, 158, 11, 0.4),
        0 4px 16px rgba(245, 158, 11, 0.2);
    border-color: rgba(255,255,255,0.5);
}

.vehicle-card-status.warning {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 50%, #b91c1c 100%);
    color: white;
    box-shadow:
        0 8px 32px rgba(239, 68, 68, 0.4),
        0 4px 16px rgba(239, 68, 68, 0.2);
    border-color: rgba(255,255,255,0.5);
}

.vehicle-card-status.bad {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 50%, #991b1b 100%);
    color: white;
    box-shadow:
        0 8px 32px rgba(220, 38, 38, 0.5),
        0 4px 16px rgba(220, 38, 38, 0.3);
    border-color: rgba(255,255,255,0.5);
    animation: pulse-danger 2s infinite;
}

.vehicle-card-status:not(.good):not(.fair):not(.warning):not(.bad) {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 50%, #1d4ed8 100%);
    color: white;
    box-shadow:
        0 8px 32px rgba(59, 130, 246, 0.4),
        0 4px 16px rgba(59, 130, 246, 0.2);
    border-color: rgba(255,255,255,0.5);
}

/* Animation pour les statuts critiques */
@keyframes pulse-danger {
    0%, 100% {
        transform: scale(1);
        box-shadow:
            0 8px 32px rgba(220, 38, 38, 0.5),
            0 4px 16px rgba(220, 38, 38, 0.3);
    }
    50% {
        transform: scale(1.05);
        box-shadow:
            0 12px 40px rgba(220, 38, 38, 0.7),
            0 6px 20px rgba(220, 38, 38, 0.4);
    }
}

/* Nouveaux badges de statut modernes */
.modern-status-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.25rem;
    border-radius: 50px;
    font-size: 0.8rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    backdrop-filter: blur(15px);
    border: 2px solid rgba(255,255,255,0.2);
    z-index: 3;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    position: relative;
    overflow: hidden;
}

.modern-status-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.6s ease;
}

.modern-status-badge:hover::before {
    left: 100%;
}

.modern-status-badge:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 12px 40px rgba(0,0,0,0.2);
}

.status-icon {
    display: flex;
    align-items: center;
    font-size: 1.1rem;
    animation: statusIconPulse 2s ease-in-out infinite;
}

.status-text {
    font-weight: 700;
    letter-spacing: 0.5px;
}

.status-pulse {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    border-radius: 50px;
    transform: translate(-50%, -50%);
    opacity: 0;
    animation: statusPulse 3s ease-in-out infinite;
}

/* Animations pour les badges */
@keyframes statusIconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes statusPulse {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(1);
    }
    50% {
        opacity: 0.3;
        transform: translate(-50%, -50%) scale(1.2);
    }
    100% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(1.4);
    }
}

/* Couleurs pour les différents états */
.modern-status-badge.excellent {
    background: linear-gradient(135deg, #00f5a0 0%, #00d9f5 100%);
    color: white;
    border-color: rgba(0, 245, 160, 0.3);
    box-shadow: 0 8px 32px rgba(0, 245, 160, 0.3);
}

.modern-status-badge.excellent .status-pulse {
    background: linear-gradient(135deg, #00f5a0, #00d9f5);
}

.modern-status-badge.good {
    background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
    color: white;
    border-color: rgba(74, 222, 128, 0.3);
    box-shadow: 0 8px 32px rgba(74, 222, 128, 0.3);
}

.modern-status-badge.good .status-pulse {
    background: linear-gradient(135deg, #4ade80, #22c55e);
}

.modern-status-badge.warning {
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
    color: white;
    border-color: rgba(251, 191, 36, 0.3);
    box-shadow: 0 8px 32px rgba(251, 191, 36, 0.3);
}

.modern-status-badge.warning .status-pulse {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
}

.modern-status-badge.critical {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    border-color: rgba(239, 68, 68, 0.3);
    box-shadow: 0 8px 32px rgba(239, 68, 68, 0.3);
}

.modern-status-badge.critical .status-pulse {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

/* ===== CONTENU DES CARTES AMÉLIORÉ ===== */
.vehicle-card-body {
    padding: 2rem 2.5rem 2.5rem;
    flex: 1;
    display: flex;
    flex-direction: column;
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    position: relative;
    z-index: 1;
}

/* Ligne de séparation élégante */
.vehicle-card-body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    width: 60%;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.3), transparent);
    transform: translateX(-50%);
    border-radius: 2px;
}

/* Effet de brillance sur le contenu */
.vehicle-card-body::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(255,255,255,0.1) 0%,
        transparent 50%,
        rgba(102, 126, 234, 0.05) 100%
    );
    pointer-events: none;
    border-radius: 0 0 28px 28px;
}

.vehicle-card-title {
    font-size: 1.5rem;
    font-weight: 900;
    margin-bottom: 0.5rem;
    color: #1a202c;
    letter-spacing: -0.025em;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
}

.vehicle-card:hover .vehicle-card-title {
    transform: translateY(-2px);
    filter: drop-shadow(0 4px 8px rgba(102, 126, 234, 0.2));
}

.vehicle-card-subtitle {
    font-size: 1rem;
    color: #64748b;
    margin-bottom: 1.5rem;
    font-weight: 600;
    position: relative;
    z-index: 2;
    transition: color 0.3s ease;
}

.vehicle-card:hover .vehicle-card-subtitle {
    color: #475569;
}

/* ===== SECTION INFORMATIONS VÉHICULE AMÉLIORÉE ===== */
.vehicle-card-info {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.25rem;
    margin-bottom: 2rem;
    padding: 2rem 1.75rem;
    background: linear-gradient(145deg, rgba(248, 250, 252, 0.95) 0%, rgba(255,255,255,0.98) 100%);
    border-radius: 28px;
    border: 2px solid rgba(255,255,255,0.98);
    box-shadow:
        inset 0 2px 8px rgba(0,0,0,0.02),
        0 12px 35px rgba(0,0,0,0.08),
        0 4px 12px rgba(0,0,0,0.04);
    backdrop-filter: blur(20px);
    position: relative;
    overflow: hidden;
}

/* Effet de brillance sur la section info */
.vehicle-card-info::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.8s ease;
}

.vehicle-card:hover .vehicle-card-info::before {
    left: 100%;
}

/* ===== ÉLÉMENTS D'INFORMATION MODERNISÉS ===== */
.vehicle-card-info-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    position: relative;
    padding: 1rem 0.75rem;
    border-radius: 20px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    background: linear-gradient(145deg, rgba(255,255,255,0.7) 0%, rgba(248,250,252,0.8) 100%);
    border: 2px solid rgba(255,255,255,0.9);
    overflow: hidden;
}

/* Effet de brillance sur les items */
.vehicle-card-info-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.6s ease;
}

.vehicle-card-info-item:hover::before {
    left: 100%;
}

.vehicle-card-info-item:hover {
    transform: translateY(-5px) scale(1.02);
    background: linear-gradient(145deg, rgba(255,255,255,0.9) 0%, rgba(248,250,252,0.95) 100%);
    box-shadow:
        0 12px 30px rgba(0,0,0,0.1),
        0 4px 12px rgba(102, 126, 234, 0.1);
    border-color: rgba(102, 126, 234, 0.3);
}

.vehicle-card-info-label {
    font-size: 0.7rem;
    color: #64748b;
    margin-bottom: 0.75rem;
    text-transform: uppercase;
    font-weight: 800;
    letter-spacing: 1.2px;
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    justify-content: center;
}

.vehicle-card-info-label i {
    font-size: 1.1rem;
    color: #667eea;
    transition: all 0.3s ease;
}

.vehicle-card-info-item:hover .vehicle-card-info-label i {
    transform: scale(1.2);
    color: #764ba2;
}

.vehicle-card-info-value {
    font-size: 0.95rem;
    font-weight: 900;
    color: #1e293b;
    line-height: 1.4;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    z-index: 2;
    text-align: center;
    word-break: break-word;
}

/* ===== STYLES SPÉCIAUX POUR LES TYPES D'INFORMATIONS ===== */

/* Style pour les dates */
.vehicle-card-info-item:has(.bx-calendar-check),
.vehicle-card-info-item:has(.bx-calendar-star) {
    background: linear-gradient(145deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.05) 100%);
    border-color: rgba(16, 185, 129, 0.2);
}

.vehicle-card-info-item:has(.bx-calendar-check):hover,
.vehicle-card-info-item:has(.bx-calendar-star):hover {
    background: linear-gradient(145deg, rgba(16, 185, 129, 0.15) 0%, rgba(5, 150, 105, 0.1) 100%);
    border-color: rgba(16, 185, 129, 0.4);
    box-shadow:
        0 12px 30px rgba(16, 185, 129, 0.15),
        0 4px 12px rgba(16, 185, 129, 0.1);
}

.vehicle-card-info-item:has(.bx-calendar-check) .vehicle-card-info-label i,
.vehicle-card-info-item:has(.bx-calendar-star) .vehicle-card-info-label i {
    color: #10b981;
}

.vehicle-card-info-item:has(.bx-calendar-check):hover .vehicle-card-info-label i,
.vehicle-card-info-item:has(.bx-calendar-star):hover .vehicle-card-info-label i {
    color: #059669;
}

/* Style pour le montant */
.vehicle-card-info-item:has(.bx-money-withdraw) {
    background: linear-gradient(145deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.05) 100%);
    border-color: rgba(245, 158, 11, 0.2);
}

.vehicle-card-info-item:has(.bx-money-withdraw):hover {
    background: linear-gradient(145deg, rgba(245, 158, 11, 0.15) 0%, rgba(217, 119, 6, 0.1) 100%);
    border-color: rgba(245, 158, 11, 0.4);
    box-shadow:
        0 12px 30px rgba(245, 158, 11, 0.15),
        0 4px 12px rgba(245, 158, 11, 0.1);
}

.vehicle-card-info-item:has(.bx-money-withdraw) .vehicle-card-info-label i {
    color: #f59e0b;
}

.vehicle-card-info-item:has(.bx-money-withdraw):hover .vehicle-card-info-label i {
    color: #d97706;
}

.vehicle-card-info-item:has(.bx-money-withdraw) .vehicle-card-info-value {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 50%, #b45309 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 900;
}

/* Animation spéciale pour le montant */
.vehicle-card-info-item:has(.bx-money-withdraw) .vehicle-card-info-value::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #f59e0b, #d97706);
    transition: all 0.4s ease;
    transform: translateX(-50%);
    border-radius: 2px;
}

.vehicle-card-info-item:has(.bx-money-withdraw):hover .vehicle-card-info-value::after {
    width: 80%;
}

/* ===== FOOTER DES CARTES AMÉLIORÉ ===== */
.vehicle-card-footer {
    padding: 2rem 2.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 30%, #f093fb 70%, #f5576c 100%);
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
    border-radius: 0 0 28px 28px;
    position: relative;
    overflow: hidden;
    box-shadow:
        inset 0 2px 4px rgba(255,255,255,0.1),
        0 -4px 12px rgba(0,0,0,0.1);
}

/* Ligne de séparation élégante */
.vehicle-card-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    width: 80%;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transform: translateX(-50%);
    border-radius: 2px;
}

/* Effet de mouvement sur le footer */
.vehicle-card-footer::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 1s ease;
}

.vehicle-card:hover .vehicle-card-footer::after {
    left: 100%;
}

.vehicle-card-department {
    font-size: 0.9rem;
    color: white;
    font-weight: 800;
    padding: 0.75rem 1.25rem;
    background: rgba(255,255,255,0.25);
    border-radius: 25px;
    border: 2px solid rgba(255,255,255,0.4);
    backdrop-filter: blur(15px);
    max-width: 60%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    box-shadow:
        0 4px 15px rgba(0,0,0,0.15),
        inset 0 1px 2px rgba(255,255,255,0.2);
    transition: all 0.3s ease;
    position: relative;
    z-index: 2;
}

.vehicle-card-department:hover {
    transform: translateY(-2px);
    background: rgba(255,255,255,0.35);
    box-shadow:
        0 6px 20px rgba(0,0,0,0.2),
        inset 0 1px 2px rgba(255,255,255,0.3);
}

/* ===== BOUTONS D'ACTION MODERNES ===== */
.vehicle-card-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    position: relative;
    z-index: 3;
}

.vehicle-card-actions .btn-action {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    text-decoration: none;
    box-shadow:
        0 6px 20px rgba(0,0,0,0.15),
        0 2px 8px rgba(0,0,0,0.1);
    backdrop-filter: blur(15px);
    position: relative;
    overflow: hidden;
    border: 2px solid rgba(255,255,255,0.3);
}

/* Effet de brillance sur les boutons */
.vehicle-card-actions .btn-action::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.5s ease;
}

.vehicle-card-actions .btn-action:hover::before {
    left: 100%;
}

.vehicle-card-actions .btn-action.view {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow:
        0 6px 20px rgba(102, 126, 234, 0.3),
        0 2px 8px rgba(102, 126, 234, 0.2);
}

.vehicle-card-actions .btn-action.view:hover {
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
    transform: scale(1.15) rotate(8deg);
    box-shadow:
        0 10px 30px rgba(102, 126, 234, 0.5),
        0 4px 12px rgba(102, 126, 234, 0.3);
}

.vehicle-card-actions .btn-action.edit {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    box-shadow:
        0 6px 20px rgba(16, 185, 129, 0.3),
        0 2px 8px rgba(16, 185, 129, 0.2);
}

.vehicle-card-actions .btn-action.edit:hover {
    background: linear-gradient(135deg, #059669 0%, #10b981 100%);
    transform: scale(1.15) rotate(-8deg);
    box-shadow:
        0 10px 30px rgba(16, 185, 129, 0.5),
        0 4px 12px rgba(16, 185, 129, 0.3);
}

.vehicle-card-actions .btn-action.delete {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    box-shadow:
        0 6px 20px rgba(239, 68, 68, 0.3),
        0 2px 8px rgba(239, 68, 68, 0.2);
}

.vehicle-card-actions .btn-action.delete:hover {
    background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
    transform: scale(1.15) rotate(8deg);
    box-shadow:
        0 10px 30px rgba(239, 68, 68, 0.5),
        0 4px 12px rgba(239, 68, 68, 0.3);
}

.vehicle-card-actions .btn-action i {
    font-size: 1.3rem;
    transition: all 0.4s ease;
    position: relative;
    z-index: 2;
}

.vehicle-card-actions .btn-action:hover i {
    transform: scale(1.3);
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
}

/* ===== ANIMATIONS ET EFFETS FINAUX ===== */

/* Animation de chargement pour les cartes */
@keyframes cardFadeIn {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
        filter: blur(5px);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
        filter: blur(0);
    }
}

/* Animation d'apparition en cascade */
.vehicle-card {
    animation: cardFadeIn 0.8s cubic-bezier(0.4, 0, 0.2, 1) both;
}

.vehicle-card:nth-child(1) { animation-delay: 0.1s; }
.vehicle-card:nth-child(2) { animation-delay: 0.2s; }
.vehicle-card:nth-child(3) { animation-delay: 0.3s; }
.vehicle-card:nth-child(4) { animation-delay: 0.4s; }
.vehicle-card:nth-child(5) { animation-delay: 0.5s; }
.vehicle-card:nth-child(6) { animation-delay: 0.6s; }
.vehicle-card:nth-child(7) { animation-delay: 0.7s; }
.vehicle-card:nth-child(8) { animation-delay: 0.8s; }

/* Animation de pulsation pour les éléments importants */
@keyframes gentlePulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.02);
        opacity: 0.9;
    }
}

/* Effet de focus pour l'accessibilité */
.vehicle-card:focus-within {
    outline: 3px solid rgba(102, 126, 234, 0.5);
    outline-offset: 4px;
    transform: translateY(-8px);
}

/* ===== RESPONSIVE DESIGN AMÉLIORÉ ===== */

/* Tablettes */
@media (max-width: 768px) {
    .vehicle-card-info {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 1.5rem;
    }

    .vehicle-card-info-item {
        padding: 1.25rem 1rem;
    }

    .vehicle-card-info-label {
        font-size: 0.8rem;
        margin-bottom: 0.5rem;
    }

    .vehicle-card-info-value {
        font-size: 1rem;
    }
}

/* Mobiles */
@media (max-width: 480px) {
    .vehicle-card {
        min-height: 520px;
        margin-bottom: 1rem;
    }

    .vehicle-card-img {
        height: 200px;
    }

    .vehicle-card-body {
        padding: 1.25rem 1.5rem 1.75rem;
    }

    .vehicle-card-info {
        grid-template-columns: 1fr;
        gap: 0.75rem;
        padding: 1.25rem;
        margin-bottom: 1.25rem;
    }

    .vehicle-card-info-item {
        padding: 1rem 0.75rem;
        border-radius: 16px;
    }

    .vehicle-card-info-label {
        font-size: 0.7rem;
        margin-bottom: 0.5rem;
        flex-direction: column;
        gap: 0.25rem;
    }

    .vehicle-card-info-label i {
        font-size: 1.2rem;
    }

    .vehicle-card-info-value {
        font-size: 0.9rem;
        line-height: 1.3;
    }

    .vehicle-card-footer {
        padding: 1.25rem 1.5rem;
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .vehicle-card-department {
        max-width: 100%;
        font-size: 0.8rem;
        padding: 0.6rem 1rem;
    }

    .vehicle-card-actions {
        gap: 0.75rem;
    }

    .vehicle-card-actions .btn-action {
        width: 40px;
        height: 40px;
    }

    .vehicle-card-actions .btn-action i {
        font-size: 1.1rem;
    }
}

/* Très petits écrans */
@media (max-width: 360px) {
    .vehicle-card-info-label {
        font-size: 0.65rem;
        letter-spacing: 0.8px;
    }

    .vehicle-card-info-value {
        font-size: 0.85rem;
    }

    .vehicle-card-actions .btn-action {
        width: 36px;
        height: 36px;
    }

    .vehicle-card-actions .btn-action i {
        font-size: 1rem;
    }
}

/* Effet de survol global pour la grille */
#gridView:hover .vehicle-card:not(:hover) {
    opacity: 0.7;
    transform: scale(0.98);
}

/* Transition fluide pour tous les éléments */
* {
    transition: opacity 0.3s ease, transform 0.3s ease;
}

/* ===== ANIMATIONS SPÉCIALES POUR LES INFORMATIONS ===== */

/* Animation de pulsation pour les dates importantes */
@keyframes datePulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.9;
    }
}

/* Animation pour les montants */
@keyframes moneyShine {
    0% {
        background-position: -200% center;
    }
    100% {
        background-position: 200% center;
    }
}

/* Application des animations */
.vehicle-card-info-item:has(.bx-calendar-check) .vehicle-card-info-value,
.vehicle-card-info-item:has(.bx-calendar-star) .vehicle-card-info-value {
    animation: datePulse 3s ease-in-out infinite;
}

.vehicle-card-info-item:has(.bx-money-withdraw) .vehicle-card-info-value {
    background: linear-gradient(
        90deg,
        #f59e0b 0%,
        #d97706 25%,
        #fbbf24 50%,
        #d97706 75%,
        #f59e0b 100%
    );
    background-size: 200% auto;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: moneyShine 3s linear infinite;
}

/* Effet de focus amélioré pour les informations */
.vehicle-card-info-item:focus-within {
    outline: 2px solid rgba(102, 126, 234, 0.5);
    outline-offset: 2px;
    transform: translateY(-3px) scale(1.02);
}

/* Amélioration de la performance avec will-change */
.vehicle-card {
    will-change: transform, box-shadow;
}

.vehicle-card:hover {
    will-change: auto;
}

.vehicle-card-info-item {
    will-change: transform, background, box-shadow;
}

.vehicle-card-info-item:hover {
    will-change: auto;
}
.vehicle-card:nth-child(3) { animation-delay: 0.3s; }
.vehicle-card:nth-child(4) { animation-delay: 0.4s; }
.vehicle-card:nth-child(5) { animation-delay: 0.5s; }
.vehicle-card:nth-child(6) { animation-delay: 0.6s; }

/* Options de tri pour la grille */
.grid-sort-options {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.grid-sort-options .form-select {
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
    background-color: white;
    transition: all 0.2s ease;
}

.grid-sort-options .form-select:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.grid-sort-options .btn {
    border: 1px solid #d1d5db;
    background: white;
    color: #6b7280;
    transition: all 0.2s ease;
}

.grid-sort-options .btn:hover {
    background: #f3f4f6;
    border-color: #9ca3af;
    color: #374151;
}

/* Carte de contrôles de vue */
.view-controls-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.1);
    border: 1px solid var(--gray-200);
}

.view-info h6 {
    color: var(--gray-800);
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.view-info small {
    color: var(--gray-600);
    font-size: 0.875rem;
}

/* Switch vue tableau/grille */
.view-switch {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.view-switch-label {
    font-size: 0.875rem;
    color: var(--gray-600);
    font-weight: 500;
}

.view-switch-buttons {
    display: flex;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: 1px solid var(--gray-300);
}

.view-switch-btn {
    padding: 0.75rem 1rem;
    background-color: white;
    border: none;
    color: var(--gray-600);
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    border-right: 1px solid var(--gray-300);
}

.view-switch-btn:last-child {
    border-right: none;
}

.view-switch-btn:hover {
    background-color: var(--gray-100);
    color: var(--gray-800);
}

.view-switch-btn.active {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
}

.view-switch-btn.active:hover {
    background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
    color: white;
}

.view-switch-btn i {
    font-size: 1rem;
}

.view-switch-btn span {
    font-weight: 600;
}

/* Loader */
.loader {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100px;
}

.loader-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(67, 97, 238, 0.1);
    border-radius: 50%;
    border-top: 4px solid var(--primary-color);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Tooltip */
.tooltip-inner {
    background-color: var(--dark-color);
    border-radius: 0.25rem;
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.bs-tooltip-auto[x-placement^=top] .arrow::before,
.bs-tooltip-top .arrow::before {
    border-top-color: var(--dark-color);
}

/* Correction pour l'affichage dans le dashboard */
.page-content {
    min-height: calc(100vh - 200px) !important;
    overflow-x: hidden !important;
    position: relative !important;
    z-index: 1 !important;
    padding: 1.5rem !important;
}

.page-wrapper {
    overflow-x: hidden !important;
}

/* Assurer que le contenu ne déborde pas */
.container-fluid {
    max-width: 100% !important;
    overflow-x: hidden !important;
}

/* Boutons d'action personnalisés pour le tableau */
.btn-group-custom {
    display: flex;
    gap: 0.25rem;
}

.btn-action-custom {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    font-size: 0.875rem;
    border: none;
    text-decoration: none;
}

.btn-action-custom:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.btn-view-custom {
    background-color: rgba(67, 97, 238, 0.1);
    color: var(--primary-color);
}

.btn-view-custom:hover {
    background-color: var(--primary-color);
    color: white;
}

.btn-edit-custom {
    background-color: rgba(255, 152, 0, 0.1);
    color: #e65100;
}

.btn-edit-custom:hover {
    background-color: #e65100;
    color: white;
}

.btn-delete-custom {
    background-color: rgba(244, 67, 54, 0.1);
    color: #b71c1c;
}

.btn-delete-custom:hover {
    background-color: #b71c1c;
    color: white;
}

/* Miniature des véhicules dans le tableau */
.vehicle-thumbnail {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: var(--border-radius);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Pagination moderne */
.modern-pagination-container {
    margin-top: 2rem;
    padding: 2rem;
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0,0,0,0.1);
    border: 1px solid rgba(255,255,255,0.8);
    backdrop-filter: blur(10px);
}

.pagination-controls {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.pagination-info-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.pagination-summary {
    display: flex;
    align-items: center;
}

.pagination-text {
    font-size: 0.95rem;
    color: #4a5568;
    font-weight: 500;
    line-height: 1.5;
}

.pagination-text strong {
    color: #2d3748;
    font-weight: 700;
}

.per-page-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: white;
    padding: 0.75rem 1rem;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    border: 2px solid rgba(102, 126, 234, 0.1);
    transition: all 0.3s ease;
}

.per-page-selector:hover {
    border-color: rgba(102, 126, 234, 0.3);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.15);
}

.per-page-label,
.per-page-suffix {
    font-size: 0.875rem;
    color: #4a5568;
    font-weight: 600;
    white-space: nowrap;
}

.per-page-select {
    border: none;
    background: transparent;
    font-weight: 700;
    color: #667eea;
    font-size: 0.875rem;
    padding: 0.25rem 0.5rem;
    min-width: 60px;
    text-align: center;
}

.per-page-select:focus {
    box-shadow: none;
    border: none;
    outline: none;
}

.pagination-navigation {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.modern-pagination {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0;
    padding: 0;
    list-style: none;
}

.modern-pagination .page-item {
    margin: 0;
}

.modern-pagination .page-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.875rem;
    color: #4a5568;
    background: white;
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    position: relative;
    overflow: hidden;
}

.modern-pagination .page-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.5s ease;
}

.modern-pagination .page-link:hover::before {
    left: 100%;
}

.modern-pagination .page-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.25);
    color: #667eea;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.modern-pagination .page-item.active .page-link {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    transform: translateY(-2px);
}

.modern-pagination .page-item.disabled .page-link {
    color: #a0aec0;
    background: #f7fafc;
    cursor: not-allowed;
    box-shadow: none;
}

.modern-pagination .page-item.disabled .page-link:hover {
    transform: none;
    box-shadow: none;
    background: #f7fafc;
    color: #a0aec0;
}

.modern-pagination .first-page,
.modern-pagination .last-page,
.modern-pagination .prev-page,
.modern-pagination .next-page {
    background: linear-gradient(135deg, #4299e1, #3182ce);
    color: white;
    font-size: 1rem;
}

.modern-pagination .first-page:hover,
.modern-pagination .last-page:hover,
.modern-pagination .prev-page:hover,
.modern-pagination .next-page:hover {
    background: linear-gradient(135deg, #3182ce, #2c5282);
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(66, 153, 225, 0.4);
}

.modern-pagination .dots {
    background: transparent;
    box-shadow: none;
    color: #a0aec0;
    cursor: default;
}

.modern-pagination .dots:hover {
    background: transparent;
    transform: none;
    box-shadow: none;
}

.quick-jump {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    background: white;
    padding: 1rem 1.25rem;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    border: 2px solid rgba(102, 126, 234, 0.1);
}

.quick-jump-label {
    font-size: 0.875rem;
    color: #4a5568;
    font-weight: 600;
    white-space: nowrap;
}

.page-jump-input {
    width: 80px;
    height: 40px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    text-align: center;
    font-weight: 600;
    color: #2d3748;
    font-size: 0.875rem;
    transition: all 0.3s ease;
}

.page-jump-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    outline: none;
}

.quick-jump .btn {
    height: 40px;
    width: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid #667eea;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    transition: all 0.3s ease;
}

.quick-jump .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
    background: linear-gradient(135deg, #764ba2, #667eea);
}

/* Responsive pour la pagination */
@media (max-width: 768px) {
    .pagination-info-section {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }

    .pagination-navigation {
        flex-direction: column;
        gap: 1rem;
    }

    .modern-pagination {
        justify-content: center;
        flex-wrap: wrap;
    }

    .modern-pagination .page-link {
        width: 40px;
        height: 40px;
        font-size: 0.8rem;
    }

    .quick-jump {
        justify-content: center;
        flex-wrap: wrap;
    }
}

/* Animations d'apparition des cartes */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
    opacity: 0;
}

/* Indicateur de chargement */
.loading-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-indicator span {
    font-size: 1.1rem;
    color: #4a5568;
    font-weight: 600;
}

/* Amélioration des transitions pour les liens de pagination */
.modern-pagination .page-link {
    position: relative;
    overflow: hidden;
}

.modern-pagination .page-link::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
}

.modern-pagination .page-link:hover::after {
    width: 100%;
    height: 100%;
}

/* Effet de survol amélioré pour les cartes */
.vehicle-card {
    position: relative;
    overflow: hidden;
}

.vehicle-card::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: rotate(45deg);
    transition: all 0.6s ease;
    opacity: 0;
}

.vehicle-card:hover::after {
    animation: shimmer 1.5s ease-in-out;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%) translateY(-100%) rotate(45deg);
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: translateX(100%) translateY(100%) rotate(45deg);
        opacity: 0;
    }
}

/* Utilisateur dans les cartes */
.vehicle-card-user {
    font-size: 0.85rem;
    color: rgba(255,255,255,0.9);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
}

.vehicle-card-user i {
    font-size: 1rem;
    color: rgba(255,255,255,0.8);
}

/* Animations */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.vehicle-card {
    animation: fadeInUp 0.6s ease-out;
}

.vehicle-card:nth-child(even) {
    animation: slideInRight 0.6s ease-out;
}

/* Assurer que les vues sont correctement cachées/affichées */
#tableView.d-none {
    display: none !important;
}

#gridView.d-none {
    display: none !important;
}

#tableView:not(.d-none) {
    display: block !important;
    animation: fadeInUp 0.5s ease-out;
}

#gridView:not(.d-none) {
    display: grid !important;
    animation: fadeInUp 0.5s ease-out;
}

/* Message aucun véhicule */
.no-vehicles-message {
    grid-column: 1 / -1;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.1);
    margin: 2rem 0;
}

.no-vehicles-message .btn {
    margin-top: 1rem;
}
