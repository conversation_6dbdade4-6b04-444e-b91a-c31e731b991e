@extends('admin.admin_dashboard')
@section('styles')
<!-- Dashboard Modern CSS -->
<link rel="stylesheet" href="{{ asset('css/dashboard-modern.css') }}">
<!-- Modern Receptions CSS -->
<link rel="stylesheet" href="{{ asset('css/modern-receptions.css') }}">
@endsection

@section('admin')
<div class="page-content dashboard-container">
    <!-- 1. En-tête et présentation générale -->
    <div class="welcome-banner animate-fade-in">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h2>Bienvenue, {{ Auth::user()->name }} !</h2>
                <p>Voici un aperçu de l'état actuel du parc automobile. Utilisez ce tableau de bord pour surveiller les statistiques clés, les tendances et les activités récentes.</p>
            </div>
            <div class="col-lg-4 text-end">
                <div class="filter-container justify-content-lg-end">
                    <select class="form-select form-select-sm filter-select" id="dateRangeFilter">
                        <option value="this-week">Cette semaine</option>
                        <option value="this-month" selected>Ce mois</option>
                        <option value="last-month">Mois dernier</option>
                        <option value="last-year">Année dernière</option>
                    </select>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-light dropdown-toggle export-btn" type="button" id="exportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class='bx bx-export'></i> Exporter
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="exportDropdown">
                            <li><a class="dropdown-item" href="javascript:exportData('pdf')"><i class='bx bxs-file-pdf me-2'></i>PDF</a></li>
                            <li><a class="dropdown-item" href="javascript:exportData('excel')"><i class='bx bxs-file-excel me-2'></i>Excel</a></li>
                            <li><a class="dropdown-item" href="javascript:exportData('csv')"><i class='bx bxs-file-csv me-2'></i>CSV</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 2. Section des statistiques clés avec design moderne -->
    <div class="dashboard-stats-container">
        <div class="row row-cols-1 row-cols-md-2 row-cols-xl-4 g-4 mb-4">
            <!-- Carte statistique - Total des véhicules -->
            <div class="col animate-fade-in delay-1">
                <div class="stat-card vehicles-card" onclick="window.location.href='{{ route('pageListeVehicule') }}'" style="cursor: pointer;">
                    <div class="card-body">
                        @if($vehiculesEnMaintenance > 5)
                            <div class="notification-badge">!</div>
                        @endif
                        <div class="stat-icon bg-vehicles">
                            <i class='bx bx-car'></i>
                        </div>
                        <h6 class="card-title">Total des Véhicules</h6>
                        <h3 class="stat-value" data-count="{{ $totalVehicules }}">0</h3>
                        <div class="stat-desc">
                            <span class="text-success fw-bold">✓ {{ $vehiculesOperationnels }} véhicules opérationnels</span>
                            <span class="text-muted">⚠ {{ $vehiculesEnMaintenance }} en maintenance</span>
                        </div>
                        <div class="trend-indicator {{ $tendanceVehicules >= 0 ? 'trend-up' : 'trend-down' }}"
                             title="Évolution par rapport au mois dernier">
                            <i class='bx {{ $tendanceVehicules >= 0 ? "bx-trending-up" : "bx-trending-down" }}'></i>
                            {{ $tendanceVehicules >= 0 ? '+' : '' }}{{ $tendanceVehicules }}%
                        </div>
                        <!-- Indicateur de progression circulaire -->
                        <svg class="progress-ring" style="--progress: {{ $totalVehicules > 0 ? round(($vehiculesOperationnels / $totalVehicules) * 100) : 0 }}">
                            <defs>
                                <linearGradient id="gradient-vehicles" x1="0%" y1="0%" x2="100%" y2="0%">
                                    <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
                                </linearGradient>
                            </defs>
                            <circle class="progress-ring-circle"></circle>
                            <circle class="progress-ring-progress" stroke="url(#gradient-vehicles)"></circle>
                        </svg>
                    </div>
                </div>
            </div>

            <!-- Carte statistique - Réceptions récentes -->
            <div class="col animate-fade-in delay-2">
                <div class="stat-card receptions-card" onclick="window.location.href='#'" style="cursor: pointer;">
                    <div class="card-body">
                        @if($receptionsEnAttente > 0)
                            <div class="notification-badge">{{ $receptionsEnAttente }}</div>
                        @endif
                        <div class="stat-icon bg-receptions">
                            <i class='bx bx-archive-in'></i>
                        </div>
                        <h6 class="card-title">Réceptions Récentes</h6>
                        <h3 class="stat-value" data-count="{{ $totalReceptions }}">0</h3>
                        <div class="stat-desc">
                            <span class="text-success fw-bold">✓ {{ $receptionsTerminees }} réceptions terminées</span>
                            <span class="text-muted">⏳ {{ $receptionsEnAttente }} en attente</span>
                        </div>
                        <div class="trend-indicator {{ $tendanceReceptions >= 0 ? 'trend-up' : 'trend-down' }}"
                             title="Évolution par rapport au mois dernier">
                            <i class='bx {{ $tendanceReceptions >= 0 ? "bx-trending-up" : "bx-trending-down" }}'></i>
                            {{ $tendanceReceptions >= 0 ? '+' : '' }}{{ $tendanceReceptions }}%
                        </div>
                        <!-- Indicateur de progression circulaire -->
                        <svg class="progress-ring" style="--progress: {{ $totalReceptions > 0 ? round(($receptionsTerminees / $totalReceptions) * 100) : 0 }}">
                            <defs>
                                <linearGradient id="gradient-receptions" x1="0%" y1="0%" x2="100%" y2="0%">
                                    <stop offset="0%" style="stop-color:#f97316;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#ea580c;stop-opacity:1" />
                                </linearGradient>
                            </defs>
                            <circle class="progress-ring-circle"></circle>
                            <circle class="progress-ring-progress" stroke="url(#gradient-receptions)"></circle>
                        </svg>
                    </div>
                </div>
            </div>

            <!-- Carte statistique - Directions actives -->
            <div class="col animate-fade-in delay-3">
                <div class="stat-card departments-card" onclick="window.location.href='#'" style="cursor: pointer;">
                    <div class="card-body">
                        <div class="stat-icon bg-departments">
                            <i class='bx bxs-building'></i>
                        </div>
                        <h6 class="card-title">Directions Actives</h6>
                        <h3 class="stat-value" data-count="{{ $totalDepartements }}">0</h3>
                        <div class="stat-desc">
                            <span class="text-success fw-bold">📊 Utilisation optimale du parc</span>
                            <span class="text-muted">⚡ {{ $efficaciteParc }}% d'efficacité globale</span>
                        </div>
                        <div class="trend-indicator {{ $tendanceDepartements >= 0 ? 'trend-up' : 'trend-down' }}"
                             title="Évolution par rapport au mois dernier">
                            <i class='bx {{ $tendanceDepartements >= 0 ? "bx-trending-up" : "bx-trending-down" }}'></i>
                            {{ $tendanceDepartements >= 0 ? '+' : '' }}{{ $tendanceDepartements }}%
                        </div>
                        <!-- Indicateur de progression circulaire -->
                        <svg class="progress-ring" style="--progress: {{ $efficaciteParc }}">
                            <defs>
                                <linearGradient id="gradient-departments" x1="0%" y1="0%" x2="100%" y2="0%">
                                    <stop offset="0%" style="stop-color:#14b8a6;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#06b6d4;stop-opacity:1" />
                                </linearGradient>
                            </defs>
                            <circle class="progress-ring-circle"></circle>
                            <circle class="progress-ring-progress" stroke="url(#gradient-departments)"></circle>
                        </svg>
                    </div>
                </div>
            </div>

            <!-- Carte statistique - Personnel -->
            <div class="col animate-fade-in delay-4">
                <div class="stat-card personnel-card" onclick="window.location.href='#'" style="cursor: pointer;">
                    <div class="card-body">
                        <div class="stat-icon bg-personnel">
                            <i class='bx bx-group'></i>
                        </div>
                        <h6 class="card-title">Personnel de la DAF</h6>
                        <h3 class="stat-value" data-count="{{ $totalEmployeesDAF }}">0</h3>
                        <div class="stat-desc">
                            <span class="text-success fw-bold">🚗 {{ $chauffeurs }} chauffeurs actifs</span>
                            <span class="text-muted">👥 {{ $utilisateurs }} utilisateurs système</span>
                        </div>
                        <div class="trend-indicator {{ $tendancePersonnel >= 0 ? 'trend-up' : 'trend-down' }}"
                             title="Évolution par rapport au mois dernier">
                            <i class='bx {{ $tendancePersonnel >= 0 ? "bx-trending-up" : "bx-trending-down" }}'></i>
                            {{ $tendancePersonnel >= 0 ? '+' : '' }}{{ $tendancePersonnel }}%
                        </div>
                        <!-- Indicateur de progression circulaire -->
                        <svg class="progress-ring" style="--progress: {{ $totalEmployeesDAF > 0 ? round(($chauffeurs / $totalEmployeesDAF) * 100) : 0 }}">
                            <defs>
                                <linearGradient id="gradient-personnel" x1="0%" y1="0%" x2="100%" y2="0%">
                                    <stop offset="0%" style="stop-color:#a855f7;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:1" />
                                </linearGradient>
                            </defs>
                            <circle class="progress-ring-circle"></circle>
                            <circle class="progress-ring-progress" stroke="url(#gradient-personnel)"></circle>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- 3. Section des graphiques interactifs -->
    <div class="row mb-4">
        <!-- Graphique principal - Évolution des réceptions -->
        <div class="col-lg-8 animate-fade-in delay-1">
            <div class="chart-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5>Évolution des réceptions</h5>
                    <div class="chart-legend">
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: rgba(30, 136, 229, 1)"></div>
                            <span>Réceptions</span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="receptionChart" data-chart-data="{{ json_encode($receptionsParMois) }}"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Graphique circulaire - Répartition des véhicules -->
        <div class="col-lg-4 animate-fade-in delay-2">
            <div class="chart-card">
                <div class="card-header">
                    <h5>Répartition des Véhicules par rapport à leur Etat</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="vehicleTypeChart" data-chart-data="{{ json_encode($vehiculesParEtatComplet) }}"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Graphique à barres - Utilisation par département -->
        <div class="col-lg-8 animate-fade-in delay-3">
            <div class="chart-card">
                <div class="card-header">
                    <h5>Affectation des Véhicules par Direction</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="departmentChart" data-chart-data="{{ json_encode($vehiculesParDepartement) }}"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Widget Événements à venir - Côté droit du graphique -->
        <div class="col-lg-4 animate-fade-in delay-3">
            <div class="side-widget">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5>Événements à venir</h5>
                    <button class="btn btn-sm btn-outline-primary"><i class='bx bx-plus'></i></button>
                </div>
                <div class="card-body">
                    <div class="calendar-date">
                        <div class="calendar-day">
                            <span class="day">02</span>
                            <span class="month">JUIN</span>
                        </div>
                        <div class="calendar-event">
                            <h6>Entretien Toyota Hilux</h6>
                            <p>Garage Central, 9h00</p>
                        </div>
                    </div>
                    <div class="calendar-date">
                        <div class="calendar-day">
                            <span class="day">05</span>
                            <span class="month">JUIN</span>
                        </div>
                        <div class="calendar-event">
                            <h6>Contrôle technique Ford Ranger</h6>
                            <p>Centre Auto, 14h30</p>
                        </div>
                    </div>
                    <div class="calendar-date">
                        <div class="calendar-day">
                            <span class="day">10</span>
                            <span class="month">JUIN</span>
                        </div>
                        <div class="calendar-event">
                            <h6>Réception nouveau véhicule</h6>
                            <p>Concessionnaire, 10h00</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 4. Section des activités récentes -->
    <div class="row mb-4">
        <div class="col-lg-8 animate-fade-in delay-1">
            <div class="modern-activity-card">
                <div class="card-header-modern d-flex justify-content-between align-items-center">
                    <div class="header-title-section">
                        <h5 class="card-title-modern">
                            <i class='bx bx-receipt me-2'></i>
                            Réceptions récentes
                        </h5>
                        <p class="card-subtitle-modern">Dernières réceptions enregistrées dans le système</p>
                    </div>
                    <div class="header-actions">
                        <a href="{{ route('liste_reception') }}" class="btn-modern btn-primary-modern">
                            <i class='bx bx-list-ul me-1'></i>
                            Voir tout
                        </a>
                    </div>
                </div>
                <div class="card-body-modern">
                    @forelse($recentReceptions as $reception)
                    <div class="reception-item-modern">
                        <div class="reception-icon-modern">
                            <div class="icon-wrapper-modern {{ $reception->pv_reception ? 'completed' : 'pending' }}">
                                <i class='bx {{ $reception->pv_reception ? "bx-check-circle" : "bx-time-five" }}'></i>
                            </div>
                        </div>
                        <div class="reception-content-modern">
                            <div class="reception-header-modern">
                                <h6 class="reception-reference-modern">{{ $reception->reference_courier }}</h6>
                                <div class="reception-status-modern">
                                    @if($reception->pv_reception)
                                        <span class="status-badge-modern completed">
                                            <i class='bx bx-check-circle me-1'></i>
                                            Terminé
                                        </span>
                                    @else
                                        <span class="status-badge-modern pending">
                                            <i class='bx bx-time-five me-1'></i>
                                            En attente
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="reception-details-modern">
                                <p class="reception-object-modern">{{ Str::limit($reception->objet, 60) }}</p>
                                <div class="reception-meta-modern">
                                    <span class="meta-item-modern">
                                        <i class='bx bx-calendar me-1'></i>
                                        {{ \Carbon\Carbon::parse($reception->date_enregistrement)->format('d/m/Y') }}
                                    </span>
                                    <span class="meta-item-modern">
                                        <i class='bx bx-building me-1'></i>
                                        {{ $reception->departement ? Str::limit($reception->departement->nom_departement, 25) : 'N/A' }}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="reception-actions-modern">
                            <a href="{{ route('reception.details', $reception->id) }}" class="action-btn-modern view" data-bs-toggle="tooltip" title="Voir détails">
                                <i class='bx bx-show'></i>
                            </a>
                            <a href="{{ route('editer_reception', $reception->id) }}" class="action-btn-modern edit" data-bs-toggle="tooltip" title="Modifier">
                                <i class='bx bx-edit'></i>
                            </a>
                        </div>
                    </div>
                    @empty
                    <div class="empty-state-modern">
                        <div class="empty-icon-modern">
                            <i class='bx bx-inbox'></i>
                        </div>
                        <h6 class="empty-title-modern">Aucune réception récente</h6>
                        <p class="empty-text-modern">Les nouvelles réceptions apparaîtront ici</p>
                        <a href="{{ route('ajouter_reception') }}" class="btn-modern btn-outline-modern">
                            <i class='bx bx-plus me-1'></i>
                            Ajouter une réception
                        </a>
                    </div>
                    @endforelse
                </div>
            </div>
        </div>

        <!-- Widget Tâches prioritaires - Côté droit des réceptions récentes -->
        <div class="col-lg-4 animate-fade-in delay-1">
            <div class="side-widget">
                <div class="card-header">
                    <h5>Tâches prioritaires</h5>
                </div>
                <div class="card-body">
                    <div class="task-item">
                        <div class="task-checkbox">
                            <input class="form-check-input" type="checkbox" id="task1">
                        </div>
                        <div class="task-content">
                            <h6>Valider les réceptions en attente</h6>
                            <p>3 réceptions à traiter</p>
                        </div>
                        <span class="task-priority priority-high">Urgent</span>
                    </div>
                    <div class="task-item">
                        <div class="task-checkbox">
                            <input class="form-check-input" type="checkbox" id="task2">
                        </div>
                        <div class="task-content">
                            <h6>Planifier les entretiens du mois</h6>
                            <p>5 véhicules concernés</p>
                        </div>
                        <span class="task-priority priority-medium">Moyen</span>
                    </div>
                    <div class="task-item">
                        <div class="task-checkbox">
                            <input class="form-check-input" type="checkbox" id="task3">
                        </div>
                        <div class="task-content">
                            <h6>Mettre à jour les documents</h6>
                            <p>Assurances et cartes grises</p>
                        </div>
                        <span class="task-priority priority-low">Faible</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 5. Section graphique employés par département -->
    <div class="row mb-4">
        <div class="col-lg-12 animate-fade-in delay-4">
            <div class="chart-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div class="header-title-section">
                        <h5 class="mb-0">
                            <i class='bx bx-group me-2'></i>
                            Répartition des Fonctionnaires par Département
                        </h5>
                        <p class="text-muted small mb-0 mt-1">Distribution du personnel dans les différents départements</p>
                    </div>
                    <div class="chart-legend">
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: rgba(52, 152, 219, 1)"></div>
                            <span>Fonctionnaires ({{ count($employesParDepartement) }} départements)</span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="height: 450px;">
                        <canvas id="employeeChart" data-chart-data="{{ json_encode($employesParDepartement) }}"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 6. Section alertes et notifications -->
    <div class="row mb-4">
        <div class="col-lg-12">
            <div class="row">
                <div class="col-lg-6">
                    <div class="alert-widget alert-warning">
                        <h6><i class='bx bx-error-circle me-2'></i>Attention</h6>
                        <p>3 véhicules nécessitent un contrôle technique dans les 30 prochains jours.</p>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="alert-widget alert-info">
                        <h6><i class='bx bx-info-circle me-2'></i>Information</h6>
                        <p>Nouvelle mise à jour du système disponible. Planifier la maintenance.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    </div>
    <!-- Toast pour les notifications -->
    <div class="position-fixed bottom-0 end-0 p-3" style="z-index: 5">
        <div id="updateToast" class="toast hide" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <strong class="me-auto">Notification</strong>
                <small>À l'instant</small>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body">
                Les données du tableau de bord ont été mises à jour avec succès.
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<!-- Dashboard Modern JS -->
<script src="{{ asset('js/dashboard-modern.js') }}"></script>
@endsection
